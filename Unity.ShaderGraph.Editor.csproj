﻿<Project>
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp\obj\$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <UseCommonOutputDirectory>true</UseCommonOutputDirectory>
    <OutputPath>Temp\bin\Debug\</OutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <LangVersion>9.0</LangVersion>
    <RootNamespace></RootNamespace>
    <OutputType>Library</OutputType>
    <AssemblyName>Unity.ShaderGraph.Editor</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup>
    <NoWarn>0169;USG0001</NoWarn>
    <DefineConstants>UNITY_6000_1_12;UNITY_6000_1;UNITY_6000;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;UNITY_2023_1_OR_NEWER;UNITY_2023_2_OR_NEWER;UNITY_2023_3_OR_NEWER;UNITY_6000_0_OR_NEWER;UNITY_6000_1_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_VIRTUALTEXTURING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_MARSHALLING_TESTS;ENABLE_VIDEO;ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;TEXTCORE_1_0_OR_NEWER;EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED;PLATFORM_STANDALONE_WIN;PLATFORM_STANDALONE;UNITY_STANDALONE_WIN;UNITY_STANDALONE;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_NVIDIA;ENABLE_AMD;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_OUT_OF_PROCESS_CRASH_HANDLER;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;GFXDEVICE_WAITFOREVENT_MESSAGEPUMP;PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER;PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION;PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS;ENABLE_MONO;NET_4_6;NET_UNITY_4_8;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER;USE_INPUT_SYSTEM_POSE_CONTROL;USE_STICK_CONTROL_THUMBSTICKS;ISDK_OPENXR_HAND;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER;UNITY_EDITOR_ONLY_COMPILATION</DefineConstants>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.23</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Editor:5</UnityProjectType>
    <UnityBuildTarget>StandaloneWindows64:19</UnityBuildTarget>
    <UnityVersion>6000.1.12f1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="C:\Users\<USER>\.vscode\extensions\visualstudiotoolsforunity.vstuc-1.1.2\Analyzers\Microsoft.Unity.Analyzers.dll" />
    <Analyzer Include="C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.SourceGenerators.dll" />
    <Analyzer Include="C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.Properties.SourceGenerator.dll" />
    <Analyzer Include="C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.UIToolkit.SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Enumerations\BlendOp.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\IMayRequireVertexID.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Advanced\AbsoluteNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Util\SerializationHelper.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Artistic\Adjustment\InvertColorsNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Descriptors\KeywordDescriptor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Derivative\DDXYNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Enumerations\Precision.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Util\PropertyUtil.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\AssetCallbacks\CreateShaderGraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Serialization\JsonRef.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\Matrix4MaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Vector\DistanceNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Controls\CubemapControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Range\MaximumNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Trigonometry\DegreesToRadiansNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Vector\ProjectionNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Basic\BooleanNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\Fullscreen\Includes\FullscreenShaderPass.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Basic\ConstantNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\StickyNoteData.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Derivative\DDYNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\SerializableGuid.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Blackboard\SGBlackboardCategory.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Descriptors\KernelDescriptor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\IGeneratesBodyCode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Colors\NoColors.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\BuiltInMetadata.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\ProceduralVirtualTextureNodePropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Util\TextUtil.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Util\TypeMapping.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\Matrix2ShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\Matrix3MaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Util\MipSamplingModes.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\NodeSettingsView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Enumerations\ZTest.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\Vector2ShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\Vector1MaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Round\SignNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Interfaces\IResizable.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\Fullscreen\FullscreenShaderGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\SerializableVirtualTexture.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Utility\RedirectNodeView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\IGroupItem.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\GraphDataUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\Slots\BooleanSlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\ShaderGraphNodeValidationExtension.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\Vector3MaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\IMayRequireDepthTexture.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\PreviewTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Descriptors\BlockFieldDescriptor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Processors\PropertyCollector.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\BitangentMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\Slots\PropertyConnectionStateSlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\Vector2MaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Scene\CameraNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\ViewModels\BlackboardViewModel.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Util\AssertHelpers.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\VectorShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Artistic\Adjustment\HueNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Enumerations\RenderType.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\ShaderGraphRequirements.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\ITargetProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Lighting\ReflectionProbeNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\IInspectable.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\Texture2DArrayPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Basic\Vector3Node.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\UV\RadialShearNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Enumerations\ShaderModel.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Texture\ProceduralVirtualTextureNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Serialization\JsonData.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Trigonometry\HyperbolicSineNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\BuiltInStructFields.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\ShaderGraphDataExtension.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Artistic\Normal\NormalBlendNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\CodeFunctionNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Controls\ObjectControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\Graph\SlotReference.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\ParentGroupChange.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Serialization\MultiJson.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Processors\ShaderGeneratorNames.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Attributes\InspectableAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\FloatField.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Manipulators\MasterPreviewManipulator.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Matrix\Matrix2Node.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Matrix\Matrix4Node.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\SerializableTexture.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Attributes\NeverAllowedByTargetAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\Vector4PropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\BuiltInStructs.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\PreviewManager.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\SampleTexture2DArrayNodePropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Util\ScreenSpaceType.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Vector\RefractNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\GradientShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Colors\CategoryColors.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Attributes\ContextFilterableAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderPreprocessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Actions\GraphViewActions.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Basic\TimeNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\DynamicMatrixMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\Graph\IOnAssetEnabled.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Colors\ColorManager.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\TargetResources\Structs.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Trigonometry\Arctangent2Node.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Texture\CubemapAssetNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\UV\SpherizeNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Util\GraphUtil.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Importers\ShaderGraphMetadata.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Texture\SampleTexture3DNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\CustomRenderTexture\CustomRenderTextureTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Trigonometry\HyperbolicCosineNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Wave\NoiseSineWaveNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\MinimalGraphData.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Controls\DefaultControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Importers\ShaderSubGraphImporterEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\CustomRenderTexture\CustomTextureSelf.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\Texture2DShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Legacy\IMasterNode1.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\IMayRequireMeshUV.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Controls\DielectricSpecularControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Descriptors\FieldDescriptor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Descriptors\StructDescriptor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Utility\Logic\AllNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Utility\CustomFunctionNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\UV\ParallaxOcclusionMappingNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Trigonometry\SineNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Trigonometry\ArctangentNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Collections\RenderStateCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\CustomFunctionNodePropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Range\SaturateNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Artistic\Adjustment\ReplaceColorNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Utility\Logic\IsNanNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\PBR\MetalReflectanceNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\Texture2DArrayInputMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\MeshDeformation\LinearBlendSkinningNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\Graph\GraphDrawingData.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\SubTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\Texture3DPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Utility\Logic\AndNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Wave\TriangleWaveNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Interface\IRequiresData.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Serialization\MultiJsonInternal.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Colors\ShaderGraphHeatmapValues.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\GeometryNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Matrix\MatrixConstructionNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\IMayRequireTime.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Controls\TextureArrayControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\PositionMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Legacy\Edge0.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\ViewModels\InspectorViewModel.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Manipulators\Scrollable.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Round\RoundNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Legacy\PBRMasterNode1.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Enumerations\KeywordScope.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Processors\ShaderSpliceUtil.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\FunctionMultiInput.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\VirtualTextureInputMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\IntegerPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\Vector4ShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Processors\GeneratorDerivativeUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\GuidEncoder.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Channel\SwizzleNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\SampleTexture3DNodePropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Controllers\SGController.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Implementation\Edge.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\NormalMapSpace.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\Canvas\CanvasStructs.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Procedural\CheckerboardNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Range\FractionNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\FloatPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\CubemapMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Colors\PrecisionColors.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Controllers\ShaderInputViewController.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Utility\Logic\IsFrontFaceNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Interpolation\SmoothstepNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Artistic\Adjustment\SaturationNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Controls\ColorControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\AbstractMaterialNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Artistic\Filter\DitherNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Utility\Logic\OrNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Util\UIUtilities.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\SearchWindowAdapter.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Extensions\IConditionalExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\EnumPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\SerializableMesh.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\ColorRGBMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Collections\AdditionalCommandCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Descriptors\RenderStateDescriptor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Basic\SubtractNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\Texture2DPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Artistic\Normal\NormalReconstructZNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Util\CopyPasteGraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Util\SpaceTransformUtil.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\ScreenPositionMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\ToggleDataPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\ShaderGraphPreferences.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGUI\BuiltInUnlitGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Descriptors\PragmaDescriptor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\ColorPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Collections\PassCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Interpolation\LerpNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\UV\ParallaxMappingNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\UV\TriplanarNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Util\KeywordCollector.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\IShaderNodeView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\IGeneratesFunction.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Scene\SceneColorNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\Slots\MultiIntegerSlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Controls\IntegerControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\Graph\INode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Util\UvChannel.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\IMayRequireCameraOpaqueTexture.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\CustomRenderTexture\CreateCustomRenderTextureShaderGraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Vector\SphereMaskNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Collections\KernelCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Descriptors\IncludeDescriptor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Manipulators\ElementResizer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Utility\Logic\BranchOnInputConnection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\Matrix2MaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\TabbedView\TabButton.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Derivative\DDXNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\MipmapStreamingShaderProperties.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\IMayRequireScreenPosition.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\Canvas\AssetCallbacks\CreateCanvasShadergraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Artistic\Normal\NormalStrengthNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\Texture2DArrayMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\ViewModels\BlackboardCategoryViewModel.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\Matrix3ShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Scene\ObjectNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\TargetResources\FieldDependencies.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\ShaderGraphHeatmapValuesEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Advanced\ExponentialNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Advanced\NormalizeNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\SerializableTextureArray.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Utility\Logic\BranchNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Enumerations\KeywordType.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Targets\BuiltInLitSubTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\IPropertyFromNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\AssetCallbacks\CreateShaderSubGraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Basic\ColorNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\MasterPreviewView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\DataStore.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Legacy\GroupData0.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\Slots\CubemapSlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Controls\Texture3DControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\Slots\ColorRGBSlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\OutputMetadata.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\LightmappingShaderProperties.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Basic\Vector2Node.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\VFXTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Util\StackPool.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\InspectorView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\IMayRequireInstanceID.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\DropdownPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Artistic\Mask\ColorMaskNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\CustomRenderTexture\CustomTextureSlice.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Processors\GraphCompilationResult.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Vector\RotateAboutAxisNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\IHasCustomDeprecationMessage.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Manipulators\Draggable.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\PropertyConnectionStateMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Util\ShaderGraphRequirementsPerKeyword.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\Texture2DArrayShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Channel\CombineNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Attributes\SubTargetFilterAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Legacy\UnlitMasterNode1.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\CubemapInputMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Legacy\SerializableGuid.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Scene\SceneDepthNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Colors\IColorProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\Texture3DShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Utility\KeywordNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\LegacyUnknownTypeNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\DynamicValueMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Matrix\MatrixDeterminantNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\ResizableElement.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Advanced\LengthNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\MatrixShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Trigonometry\RadiansToDegreesNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\SubGraph\SubGraphOutputNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Wave\SawtoothWaveNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\IMayRequireNormal.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Utility\RedirectNodeData.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\TransformNodePropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Util\IndexSet.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Range\ClampNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Utility\SubGraphNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Interfaces\IShaderInputObserver.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Gradient\BlackbodyNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Blackboard\BlackboardInputInfo.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\UVMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Basic\IntegerNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\ShaderStage.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Matrix\TransformationMatrixNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Utility\SplitTextureTransformNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\Slots\Texture3DSlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\ShaderGraphToolbarExtension.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Enumerations\DisableBatching.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Controls\TextureControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Colors\UserColors.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\Fullscreen\FullscreenData.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\Canvas\Templates\CanvasShaderGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\PropertySheet.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\SpaceMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\MaterialGraphPreviewGenerator.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\ContextView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\MatrixPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Range\MinimumNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Geometry\ViewDirectionNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\Vector4MaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawerUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Legacy\SpriteLitMasterNode1.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Texture\SampleTexture2DNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Legacy\AbstractMaterialNode0.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\IMayRequireVertexColor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\PropertyRow.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\IPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\UV\PolarCoordinatesNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\UV\TwirlNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Extensions\StencilExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Texture\Texture2DArrayAssetNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Processors\ShaderStringBuilder.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\AssetPostProcessors\MaterialPostprocessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Enumerations\Platform.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\MaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Descriptors\AdditionalCommandDescriptor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Trigonometry\HyperbolicTangentNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\Slots\LabelSlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Descriptors\PassDescriptor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Controllers\BlackboardController.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Controls\ChannelMixerControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Advanced\PosterizeNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Texture\Texture2DAssetNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Util\PooledList.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\PreviewMode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\ShaderInputPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Enumerations\InstancingOptions.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Controls\PopupControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Advanced\ReciprocalNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\ShaderPort.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGUI\BuiltInLitGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\SamplerStateMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Implementation\IHasDependencies.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Utility\Logic\IsInfiniteNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Procedural\Shape\EllipseNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Blackboard\SGBlackboardField.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Utility\Logic\NotNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Round\TruncateNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Geometry\VertexColorNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\Fullscreen\FullscreenSubTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\ViewModels\ShaderInputViewModel.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\Slots\GradientSlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Range\RandomRangeNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\IHasMetaData.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Extensions\FieldExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\PortInputView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\MaterialEditor\ShaderGraphPropertyDrawers.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Utils\TargetUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Data\KeywordEntry.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Geometry\InstanceIDNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\ShaderGraphShortcuts.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Procedural\Shape\RoundedRectangleNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\Canvas\CanvasSubTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\CustomInterpolatorNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Blackboard\BlackboardUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Enumerations\KeywordShaderStage.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Data\ConditionalField.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Enumerations\NormalDropOffSpace.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\SampleVirtualTextureNodePropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Advanced\LogNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Scene\SceneDepthDifferenceNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Artistic\Adjustment\WhiteBalanceNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Range\RemapNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Collections\StructCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Geometry\ScreenPositionNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\StickyNote.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\TabbedView\TabbedView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\PreviewProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\TargetResources\BlockFields.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\Texture3DMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\IMayRequirePositionPredisplacement.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Util\TypeMapper.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Processors\ActiveFields.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Collections\KeywordCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\GenerationMode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\GraphSubWindow.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\TextPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Serialization\JsonObject.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\IMaterialGraphAsset.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\GradientInputMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Serialization\RefValueEnumerable.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Importers\RenderPipelineChangedCallback.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Contexts\TargetActiveBlockContext.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Util\PrecisionUtil.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Legacy\GraphData0.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Controls\ChannelEnumControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\IMayObsolete.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Controls\TextControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Enumerations\ShaderValueType.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\Texture3DInputMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Advanced\ModuloNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Controls\GradientControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\Canvas\CanvasMetaData.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\IGraphDataAction.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\ColorShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\Canvas\CanvasData.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\ShaderGraphAnalytics.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Collections\DefineCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Util\Documentation.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Artistic\Adjustment\ContrastNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Data\FieldCondition.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\ViewDirectionMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\BuiltInProperties.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Target.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Advanced\NegateNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Wave\SquareWaveNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\SamplerStateNodePropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\ShaderGUI\GenericShaderGraphMaterialGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Util\ListUtilities.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Basic\AddNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\Texture2DMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\BlockNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Geometry\BitangentVectorNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\PropertyNodeView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Vector\CrossProductNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Texture\TexelSizeNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\Texture2DInputMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Blackboard\SGBlackboardRow.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\GradientPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\IMayRequirePosition.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Advanced\ReciprocalSquareRootNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Serialization\FakeJsonObject.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Round\CeilingNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Controls\ButtonControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Util\ValueUtilities.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Texture\SampleCubemapNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Enumerations\ZWrite.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Lighting\AmbientNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Geometry\NormalVectorNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\Slots\UVSlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Importers\ShaderGraphAssetPostProcessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\ShaderDropdown.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\MaterialNodeView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Utility\Logic\AnyNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\SerializableCubemap.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Geometry\UVNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Util\FileUtilities.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Procedural\Noise\SimpleNoiseNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Targets\BuiltInTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Controls\EnumConversionControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\IMaterialSlotHasValue.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Util\PooledHashSet.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Blackboard\SGBlackboard.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Range\OneMinusNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Util\RGBANodeOutput.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Controls\SliderControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Lighting\MainLightDirectionNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Basic\SliderNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Util\KeywordDependentCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Round\StepNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\NodeClassCache.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\ReorderableTextListView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\HlslFunctionView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\Vector1ShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Matrix\MatrixTransposeNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\DynamicVectorMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Enumerations\KeywordDefinition.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\IdentifierField.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\Matrix4ShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\PBR\DielectricSpecularNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\NormalMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\ReorderableSlotListView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Descriptors\SubShaderDescriptor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Utility\DropdownNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\GraphDataPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGUI\BaseShaderGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Processors\GenerationUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Controllers\BlackboardCategoryController.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Importers\ShaderSubGraphImporter.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Enumerations\Blend.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Util\GradientUtil.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\Slots\TextureArraySlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Enumerations\Cull.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Vector\TransformNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Legacy\StickyNoteData0.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Contexts\TargetFieldContext.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Scene\FogNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Artistic\Mask\ChannelMaskNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\UV\FlipbookNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\AbstractShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Interface\IConditional.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Interfaces\ISGControlledElement.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\AssetCallbacks\CreateLitShaderGraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Targets\BuiltInUnlitSubTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Serialization\RefDataEnumerable.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Basic\MultiplyNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\GraphConcretization.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Artistic\Blend\BlendMode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Enumerations\StructFieldOptions.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Texture\Texture3DAssetNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Contexts\TargetPropertyGUIContext.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Trigonometry\ArccosineNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Attributes\BuiltinKeywordAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Colors\CustomColorData.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\EdgeConnectorListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\ContextData.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\GraphDataReadOnly.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\FormerNameAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Vector\RejectionNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Util\CompatibilityExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Geometry\PositionNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Collections\PragmaCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\ShaderGraphVfxAsset.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\PreviewSceneResources.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Controls\ChannelEnumMaskControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Controls\IdentifierControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Data\DropdownEntry.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Descriptors\StencilDescriptor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\GraphEditorView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Scene\EyeIndexNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Enumerations\PropertyType.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Legacy\SlotReference0.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Trigonometry\ArcsineNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\ICanChangeShaderGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\CubemapPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\Slots\MultiFloatSlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Geometry\VertexIDNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Contexts\TargetSetupContext.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Texture\CalculateLevelOfDetailTexture2DNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Artistic\Filter\FadeTransitionNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\TargetResources\Fields.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Procedural\Shape\RectangleNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\ShaderInput.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Controls.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\MaterialGraphView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Lighting\BakedGINode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\SampleTexture2DNodePropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Trigonometry\TangentNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Processors\Generator.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\INodeModificationListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Texture\SampleRawCubemapNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Controls\ToggleControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\VirtualTextureShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Artistic\Normal\NormalFromHeightNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\GraphData.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\Slots\TextureSlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\SubGraphOutputNodePropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Basic\Vector1Node.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Util\Identifier.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Artistic\Normal\NormalFromTextureNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\GradientMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Utility\PreviewNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Texture\SampleTexture2DLODNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Procedural\Noise\GradientNoiseNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\SamplerStateShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Serialization\SerializationExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\IMayRequireVertexSkinning.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\GraphCode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\BooleanMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\AssemblyInfo.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\CustomRenderTexture\CustomTextureSize.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\GroupData.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Implementation\GraphObject.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Interfaces\ISGViewModel.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\CategoryData.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Util\MessageManager.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Implementation\SlotType.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Legacy\ShaderInput0.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Matrix\MatrixSplitNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\TextureSamplerState.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\GraphValidation.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Collections\IncludeCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Geometry\ViewVectorNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Texture\SamplerStateNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Procedural\Noise\VoronoiNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\Graph\DrawState.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\ShaderKeyword.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Collections\AssetCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\PositionSource.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\AssetVersion.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Basic\DivideNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Processors\CustomInterpolatorUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\HelpBoxRow.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Implementation\NodeUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Legacy\ILegacyTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\Vector3PropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Artistic\Normal\NormalUnpackNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\MeshDeformation\ComputeDeformNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Enumerations\IncludeLocation.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Utility\Logic\NandNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Legacy\VisualEffectMasterNode1.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Controls\VectorControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Attributes\SRPFilterAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\ColorMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\IMayRequireFaceSign.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\AssetCallbacks\CreateUnlitShaderGraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\SearchWindowProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\ShaderGroup.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Matrix\Matrix3Node.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\IMaySupportVFX.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Utility\Logic\ComparisonNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Util\Logging.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Manipulators\WindowDraggable.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\BoolPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Enumerations\RenderQueue.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\NeededCoordinateSpace.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Texture\GatherTexture2DNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\BuiltInFields.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Collections\DependencyCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\Slots\ScreenPositionSlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\VertexColorMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Targets\BuiltInCanvasSubTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\IMayRequireTransform.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Vector\ReflectionNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\PropertyNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Legacy\SpriteUnlitMasterNode1.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Channel\FlipNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\PositionNodePropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Scene\ScreenNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Importers\ShaderGraphImporter.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Basic\PowerNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\VirtualTextureMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\SlotValue.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\SubGraph\SubGraphAsset.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\RedirectNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Data\FieldDependency.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\Canvas\CanvasProperties.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\UV\RotateNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Procedural\Shape\PolygonNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\BooleanShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Gradient\SampleGradientNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\Fullscreen\FullscreenMetaData.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Util\FunctionRegistry.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Geometry\TangentVectorNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\TangentMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Interpolation\InverseLerpNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Attributes\SGPropertyDrawerAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Targets\BuiltInSubTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Texture\SampleTexture2DArrayNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\IShaderPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Implementation\HasDependenciesAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Artistic\Utility\ColorspaceConversion.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\TitleAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\TriplanarNodePropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Vector\FresnelEffectNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\WindowDockingLayout.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\Graph\IEdge.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Artistic\Adjustment\ChannelMixerNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Util\SlotValueTypeUtil.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Colors\HeatmapColors.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\MaterialGraphEditWindow.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\AbstractMaterialNodePropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Interfaces\IRectInterface.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\IMayRequireTangent.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Util\CreateSerializableGraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\Vector3ShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Gradient\GradientNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Util\KeywordUtil.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\Slots\ColorSlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Texture\SampleVirtualTextureNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Procedural\Shape\RoundedPolygonNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Views\GradientEdge.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Channel\SplitNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Importers\ShaderGraphImporterEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\AssetPostProcessors\ShaderGraphMaterialsUpdater.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\UV\TilingAndOffsetNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\IMayRequireViewDirection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\DefaultShaderIncludes.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\ShaderGraphProjectSettings.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\CustomRenderTexture\CustomTextureSubTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\CubemapShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Graphs\GraphSetup.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Vector\DotProductNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Round\FloorNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Artistic\Blend\BlendNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Interfaces\IMayRequireBitangent.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\TargetResources\StructFields.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Basic\Vector4Node.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Collections\FieldCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Serialization\MultiJsonEntry.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Importers\ShaderSubGraphMetadata.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Input\Texture\TextureStackNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Processors\MatrixNames.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Basic\SquareRootNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Data\Nodes\Math\Trigonometry\CosineNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Controls\IControlAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Attributes\GenerateBlocksAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Inspector\PropertyDrawers\Vector2PropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Drawing\Controls\EnumControl.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\SGBlackboard.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\ShaderLibrary\DeclareDepthTexture.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Shaders\BlitNoAlpha.shader" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\UnlitPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\UXML\Blackboard\SGBlackboardRow.uxml" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\TabbedView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\Controls\IntegerControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\ShaderLibrary\ShaderTypes.cs.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\ShaderLibrary\Core.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\ShaderLibrary\SurfaceInput.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Resizable.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\ShaderLibrary\MetaInput.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\Controls\ScreenPositionSlotControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\PixelCacheProfiler.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\Controls\CubemapSlotControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\MasterPreviewView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\Fullscreen\Includes\FullscreenBlit.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Templates\SharedCode.template.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\RedirectNode.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\BuildInputData.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\PBRDeferredPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\ShaderLibrary\SSAO.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\Controls\TextureArraySlotControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\PBRForwardAddPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\ShaderLibrary\Particles.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Templates\BuildVertexDescriptionInputs.template.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\ShaderPort.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\PropertyNameReferenceField.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Templates\BuildSurfaceDescriptionInputs.template.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\UXML\Blackboard\SGBlackboardField.uxml" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\ShaderLibrary\Lighting.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\LegacySurfaceVertex.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\Fullscreen\Includes\FullscreenCommon.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\HeatmapValuesEditor.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\GraphSubWindow.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\UXML\TabButton.uxml" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\MaterialNodeView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\UXML\StickyNote.uxml" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\Controls\SliderControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\ShadowCasterPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\ShaderPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\ShaderLibrary\UnityGBuffer.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\PropertyNodeView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\ShaderLibrary\Shim\InputsShim.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\Fullscreen\Templates\SharedCode.template.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\MaterialGraph.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\Controls\PopupControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\Controls\GradientSlotControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\ReorderableSlotListView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\Controls\EnumConversionControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\CustomRenderTexture\CustomTextureGraph.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\Controls\ColorRGBASlotControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\Controls\DielectricSpecularControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\Controls\TextControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\HlslFunctionView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\CustomSlotLabelField.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Templates\ShaderPass.template" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\ShaderLibrary\Shadows.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\UXML\NodeSettings.uxml" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\Controls\BooleanSlotControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\BuiltInCanvasPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\ShaderLibrary\ShaderGraphFunctions.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\InspectorView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\Varyings.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Selectable.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\UXML\HeatmapValuesEditor.uxml" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\ShaderLibrary\SurfaceData.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\Controls\MultiFloatControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\Controls\EnumControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\UXML\Blackboard\SGBlackboardCategory.uxml" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\GraphEditorView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\Controls\MultiFloatSlotControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\LightingMetaPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\StickyNote.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\NodeSettings.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\Controls\TextureSlotControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\Controls\UVSlotControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\Controls\ChannelMixerControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\ShaderLibrary\ParticlesInstancing.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\MaterialGraphView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\ColorMode.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\ShaderLibrary\Input.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\PropertyRow.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\ShaderLibrary\Shim\SurfaceShaderProxy.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\Controls\ToggleControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\ShaderLibrary\Deprecated.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\Controls\MultiIntegerSlotControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Shaders\FallbackError.shader" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\Fullscreen\Includes\FullscreenDrawProcedural.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\ShaderLibrary\Shim\Shims.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\Fullscreen\Includes\FullscreenShaderPass.cs.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\Controls\ColorControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\ShaderLibrary\ShaderVariablesFunctions.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\PortInputView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\ShaderLibrary\UnityInput.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Shaders\Checkerboard.shader" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\ShaderLibrary\Shim\HLSLSupportShim.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\SpriteUnlitPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\PropertySheet.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\TabButtonStyles.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\UXML\GraphInspector.uxml" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\Fullscreen\Includes\CustomRenderTexture.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Templates\PassMesh.template" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\Controls\ChannelEnumMaskControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\Controls\Texture3DSlotControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\Controls\GradientControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\Controls\ChannelEnumControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\DepthOnlyPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\Fullscreen\Templates\ShaderPass.template" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\UXML\GraphSubWindow.uxml" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\PBRGBufferPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\UXML\Blackboard\SGBlackboard.uxml" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\LegacyBuilding.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\ShaderLibrary\DeclareNormalsTexture.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\UXML\Resizable.uxml" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Unity.ShaderGraph.Editor.asmdef" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\UXML\PixelCacheProfiler.uxml" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\Canvas\Templates\PassUI.template" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Resources\Styles\Controls\ColorRGBSlotControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\PBRForwardPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\CustomRenderTexture\CustomTexture.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\ShaderLibrary\DeclareOpaqueTexture.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\CustomRenderTexture\CustomTextureSubShader.template" />
    <None Include="Library\PackageCache\com.unity.shadergraph@8d13f365c663\Editor\Generation\Targets\BuiltIn\ShaderLibrary\ShaderVariablesFunctions.deprecated.hlsl" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AMDModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.AMDModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.ContentLoadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GraphicsStateCollectionSerializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HierarchyCoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.HierarchyCoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputForUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputForUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MarshallingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.MarshallingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MultiplayerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.NVIDIAModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.NVIDIAModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ShaderVariantAnalyticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.ShaderVariantAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.VirtualTexturingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AccessibilityModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AdaptivePerformanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.AdaptivePerformanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.BuildProfileModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.BuildProfileModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreBusinessMetricsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreBusinessMetricsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.EditorToolbarModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EmbreeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.EmbreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphicsStateCollectionSerializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridAndSnapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridAndSnapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.MultiplayerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Physics2DModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.PresetsUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PropertiesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SafeModeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.SafeModeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.ShaderFoundryModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.ShaderFoundryModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SketchUpModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.SketchUpModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteMaskModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteShapeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SubstanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TerrainModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextRenderingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TilemapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TreeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.TreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIAutomationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIAutomationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UmbraModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VFXModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VideoModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.XRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Graphs">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEditor.Graphs.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.WebGL.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\WebGLSupport\UnityEditor.WebGL.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.OSXStandalone.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\MacStandaloneSupport\UnityEditor.OSXStandalone.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VisionOS.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\VisionOSPlayer\UnityEditor.VisionOS.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.WindowsStandalone.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\WindowsStandaloneSupport\UnityEditor.WindowsStandalone.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UWP.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\MetroSupport\UnityEditor.UWP.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Android.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\AndroidPlayer\UnityEditor.Android.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AppleTV.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\AppleTVSupport\UnityEditor.AppleTV.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.LinuxStandalone.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\LinuxStandaloneSupport\UnityEditor.LinuxStandalone.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.YamlDotNet">
      <HintPath>Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>Library\PackageCache\com.unity.collections@56bff8827a7e\Unity.Collections.LowLevel.ILSupport\Unity.Collections.LowLevel.ILSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>Library\PackageCache\com.unity.ext.nunit@031a54704bff\net40\unity-custom\nunit.framework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="unityplastic">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\unityplastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Antlr3.Runtime">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Unity.Plastic.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Newtonsoft.Json">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Unity.Plastic.Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="log4netPlastic">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\log4netPlastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.IonicZip">
      <HintPath>Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Antlr3.Runtime">
      <HintPath>Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Flow\Dependencies\NCalc\Unity.VisualScripting.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>Library\PackageCache\com.unity.nuget.newtonsoft-json@74deb55db2a0\Runtime\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.TextureAssets">
      <HintPath>Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Editor\VisualScripting.Core\EditorAssetResources\Unity.VisualScripting.TextureAssets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>Library\PackageCache\com.unity.nuget.mono-cecil@d6f9955a5d5f\Mono.Cecil.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DemiEditor">
      <HintPath>Assets\Plugins\Demigiant\DemiLib\Core\Editor\DemiEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>Assets\Plugins\Demigiant\DOTween\DOTween.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenUpgradeManager">
      <HintPath>Assets\Plugins\Demigiant\DOTween\Editor\DOTweenUpgradeManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenPro">
      <HintPath>Assets\Plugins\Demigiant\DOTweenPro\DOTweenPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DemiLib">
      <HintPath>Assets\Plugins\Demigiant\DemiLib\Core\DemiLib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenProEditor">
      <HintPath>Assets\Plugins\Demigiant\DOTweenPro\Editor\DOTweenProEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenEditor">
      <HintPath>Assets\Plugins\Demigiant\DOTween\Editor\DOTweenEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\AppleTVSupport\UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Apple.Extensions.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\AppleTVSupport\UnityEditor.Apple.Extensions.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Apple.Extensions.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.Apple.Extensions.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Types">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Types.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Gradle">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Gradle.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\VisionOSPlayer\UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Apple.Extensions.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\VisionOSPlayer\UnityEditor.Apple.Extensions.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\MacStandaloneSupport\UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.CSharp">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Microsoft.CSharp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.DataSetExtensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Buffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Annotations.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http.Rtc">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Http.Rtc.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.DispatchProxy.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Duplex">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Duplex.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Http">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.NetTcp">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.NetTcp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Security">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Unity.ShaderGraph.Utilities.csproj" />
    <ProjectReference Include="Unity.Searcher.Editor.csproj" />
    <ProjectReference Include="Unity.RenderPipelines.Core.Runtime.csproj" />
    <ProjectReference Include="Unity.RenderPipelines.Core.Editor.csproj" />
    <ProjectReference Include="UnityEditor.UI.csproj" />
    <ProjectReference Include="UnityEngine.UI.csproj" />
    <ProjectReference Include="UnityEngine.TestRunner.csproj" />
    <ProjectReference Include="UnityEditor.TestRunner.csproj" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>
