-target:library
-out:"Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.dll"
-refout:"Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.ref.dll"
-define:UNITY_6000_1_12
-define:UNITY_6000_1
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:UNITY_6000_1_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_STANDALONE_WIN
-define:PLATFORM_STANDALONE
-define:UNITY_STANDALONE_WIN
-define:UNITY_STANDALONE
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_NVIDIA
-define:ENABLE_AMD
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
-define:PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER
-define:PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION
-define:PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS
-define:ENABLE_MONO
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:USE_INPUT_SYSTEM_POSE_CONTROL
-define:USE_STICK_CONTROL_THUMBSTICKS
-define:ISDK_OPENXR_HAND
-define:DOTWEEN
-define:OVR_UNITY_PACKAGE_MANAGER
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"Assets/Plugins/Demigiant/DemiLib/Core/DemiLib.dll"
-r:"Assets/Plugins/Demigiant/DemiLib/Core/Editor/DemiEditor.dll"
-r:"Assets/Plugins/Demigiant/DOTween/DOTween.dll"
-r:"Assets/Plugins/Demigiant/DOTween/Editor/DOTweenEditor.dll"
-r:"Assets/Plugins/Demigiant/DOTweenPro/DOTweenPro.dll"
-r:"Assets/Plugins/Demigiant/DOTweenPro/Editor/DOTweenProEditor.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEditor.Graphs.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/NetStandard/ref/2.1.0/netstandard.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/PlaybackEngines/AppleTVSupport/UnityEditor.Apple.Extensions.Common.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/PlaybackEngines/AppleTVSupport/UnityEditor.AppleTV.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/PlaybackEngines/AppleTVSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.Apple.Extensions.Common.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Common.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/PlaybackEngines/LinuxStandaloneSupport/UnityEditor.LinuxStandalone.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/PlaybackEngines/MetroSupport/UnityEditor.UWP.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/PlaybackEngines/VisionOSPlayer/UnityEditor.Apple.Extensions.Common.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/PlaybackEngines/VisionOSPlayer/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/PlaybackEngines/VisionOSPlayer/UnityEditor.VisionOS.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/log4netPlastic.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/Unity.Plastic.Antlr3.Runtime.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/Unity.Plastic.Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/unityplastic.dll"
-r:"Library/PackageCache/com.unity.collections@56bff8827a7e/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Editor/VisualScripting.Core/Dependencies/DotNetZip/Unity.VisualScripting.IonicZip.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Editor/VisualScripting.Core/Dependencies/YamlDotNet/Unity.VisualScripting.YamlDotNet.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Editor/VisualScripting.Core/EditorAssetResources/Unity.VisualScripting.TextureAssets.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.ref.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.1.12f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/AssemblyInfo.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/OpenXR/HandTranslationUtils.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/OpenXR/OpenXRHandPrimitives.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/OpenXR/PalmGrabAPI.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/OpenXR/PinchGrabAPI.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Attributes/ConditionalHideAttribute.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Attributes/HelpBoxAttribute.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Attributes/InspectorButton.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Attributes/OptionalAttribute.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Attributes/SectionAttribute.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Body/BodyDebugGizmos.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Body/Input/Body.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Body/Input/BodyDataAsset.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Body/Input/BodyJointsCache.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Body/Input/BodyPrimitives.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Body/Input/BodySkeletonMapping.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Body/Input/IBody.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Body/Input/ISkeletonMapping.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Body/PoseDetection/BodyPoseComparerActiveState.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Body/PoseDetection/BodyPoseComparerActiveStateDebugVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Body/PoseDetection/BodyPoseData.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Body/PoseDetection/BodyPoseDebugGizmos.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Body/PoseDetection/IBodyPose.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Body/PoseDetection/PoseFromBody.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Body/SkeletonDebugGizmos.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Collections/EnumerableHashSet.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Collisions/ClosestPointToColliders.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Collisions/ColliderGroup.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Collisions/IsCapsuleWithinColliderApprox.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Collisions/IsPointWithinCollider.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Collisions/IsSphereWithinCollider.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/DistanceGrab/DistantPointDetector.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/DistanceGrab/IDistanceInteractor.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/DistanceGrab/Visuals/ArcTubeVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/DistanceGrab/Visuals/DistantInteractionLineRendererVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/DistanceGrab/Visuals/DistantInteractionLineVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/DistanceGrab/Visuals/DistantInteractionPolylineVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/DistanceGrab/Visuals/DistantInteractionTubeVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/DistanceGrab/Visuals/InteractorReticle.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/DistanceGrab/Visuals/IReticleData.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/DistanceGrab/Visuals/ReticleDataGhost.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/DistanceGrab/Visuals/ReticleDataIcon.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/DistanceGrab/Visuals/ReticleDataMesh.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/DistanceGrab/Visuals/ReticleGhostDrawer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/DistanceGrab/Visuals/ReticleIconDrawer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/DistanceGrab/Visuals/ReticleMeshDrawer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/DistanceGrab/Visuals/TubeRenderer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Extensions/HashSetExtensions.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Extensions/MonoBehaviourStartExtensions.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Extensions/TransformExtensions.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Extensions/VectorExtensions.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Grab/ControllerPinchInjector.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Grab/FingerPalmGrabAPI.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Grab/FingerPinchGrabAPI.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Grab/FingerRawPinchAPI.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Grab/FingerRawPinchInjector.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Grab/GrabbingRule.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Grab/GrabTypeFlags.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Grab/HandGrabAPI.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Grab/IFingerAPI.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Grab/SnapSurfaces/BezierGrabSurface.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Grab/SnapSurfaces/BoxGrabSurface.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Grab/SnapSurfaces/ColliderGrabSurface.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Grab/SnapSurfaces/CylinderGrabSurface.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Grab/SnapSurfaces/GrabPoseHelper.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Grab/SnapSurfaces/IGrabSurface.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Grab/SnapSurfaces/SphereGrabSurface.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Grab/Visuals/GrabStrengthIndicator.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Grab/Visuals/HandGhost.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Grab/Visuals/HandGhostProvider.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Grab/Visuals/HandGrabStateVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Grab/Visuals/HandJointMap.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Grab/Visuals/HandPuppet.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/HandGlow/HandFingerMaskGenerator.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/HandGlow/HandGrabGlow.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/HandGlow/HandPokeOvershootGlow.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/HandGlow/HandRayPinchGlow.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Axis1DPrioritySelector.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Axis1DSwitch.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Controllers/Controller.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Controllers/ControllerButtonUsageActiveState.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Controllers/ControllerDataAsset.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Controllers/ControllerDataSourceConfig.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Controllers/ControllerHandDataSource.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Controllers/ControllerPrimitives.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Controllers/ControllerRef.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Controllers/ControllerVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Controllers/IController.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Controllers/IControllerDataModifier.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Controllers/ScrollInputProvider.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Controllers/SyntheticControllerInHand.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/DataModifier.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/DataSource.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Hands/ControllerAnimatedHand.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Hands/DataModifiers/FixedScaleHand.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Hands/DataModifiers/HandFilter.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Hands/DataModifiers/JointRotationHistoryHand.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Hands/DataModifiers/LastKnownGoodHand.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Hands/DataModifiers/SyntheticHand.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Hands/DominantHandRef.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Hands/FingersMetadata.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Hands/FromHandPrefabDataSource.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Hands/Hand.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Hands/HandDataAsset.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Hands/HandDataSourceConfig.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Hands/HandJointsCache.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Hands/HandMirroring.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Hands/HandPhysicsCapsules.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Hands/HandRef.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Hands/HandSourceInjector.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Hands/IHand.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Hands/IHandSkeletonProvider.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Hands/JointsRadiusFeature.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Hands/OVRHandPrimitives.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/Hands/ShadowHand.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/HMD/Hmd.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/HMD/HmdDataAsset.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/HMD/HmdDataSourceConfig.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/HMD/HmdRef.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/HMD/IHmd.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/IAxis1D.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/IAxis2D.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/IButton.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/ITrackingToWorldTransformer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/OneEuroFilter/FilteredTransform.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/OneEuroFilter/HandFilterParameterBlock.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/OneEuroFilter/IOneEuroFilter.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/OneEuroFilter/OneEuroFilter.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/OneEuroFilter/OneEuroFilterFactory.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/OneEuroFilter/OneEuroFilterMulti.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/OneEuroFilter/OneEuroFilterPropertyBlock.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/PoseOrigin.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/SkeletonJointsCache.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Input/TransformTrackingToWorldTransformer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/ActiveStateGate.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/ActiveStateGroup.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/ActiveStateNot.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/ActiveStateSelector.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/ActiveStateToggle.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/ActiveStateTracker.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/Axis2DActiveState.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/Base/InteractorGroup.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/BestHoverInteractorGroup.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/BestSelectInteractorGroup.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/CandidateComparer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/CandidatePositionComparer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/CollisionInteractionRegistry.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/ControllerActiveState.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/ControllerAxis2D.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/DPadUnityEventWrapper.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/FirstHoverInteractorGroup.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/GameObjectActiveState.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/HandActiveState.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/HoverInteractorsGate.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/IActiveState.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/ICandidateComparer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/ICandidatePosition.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/ICollidersRef.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/IEvent.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/IGameObjectFilter.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/IInteractable.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/IInteractor.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/Interactable.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/InteractableGroup.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/InteractableGroupView.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/InteractableRegistry.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/InteractableState.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/InteractableTriggerBroadcaster.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/Interactor.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/InteractorActiveState.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/InteractorControllerDecorator.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/InteractorState.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/IRelativeToRef.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/IRigidbodyRef.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/ISelector.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/MultiAction.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/SecondaryInteractorConnection.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/SecondaryInteractorFilter.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/TagSet.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/TagSetFilter.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/UniqueIdentifier.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/UpdateDriverAfterDataSource.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/UpdateDriverGroup.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Core/VirtualActiveState.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Grabbable/FloatConstraint.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Grabbable/Grabbable.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Grabbable/GrabFreePhysicsTransformer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Grabbable/GrabFreeTransformer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Grabbable/IGrabbable.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Grabbable/ITransformer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Grabbable/OneGrabFreeTransformer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Grabbable/OneGrabPhysicsJointTransformer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Grabbable/OneGrabRotateTransformer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Grabbable/OneGrabSphereTransformer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Grabbable/OneGrabTranslateTransformer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Grabbable/TwoGrabFreeTransformer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Grabbable/TwoGrabPlaneTransformer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Grabbable/TwoGrabRotateTransformer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/DistanceGrab/DistanceGrabInteractable.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/DistanceGrab/DistanceGrabInteractor.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/DistanceGrab/DistantCandidateComputer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/DistanceHandGrab/DistanceHandGrabInteractable.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/DistanceHandGrab/DistanceHandGrabInteractor.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Grab/GrabInteractable.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Grab/GrabInteractor.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Grab/Tween.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/HandGrab/GrabPoseFinder.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/HandGrab/GrabPoseScore.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/HandGrab/HandGrabInteractable.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/HandGrab/HandGrabInteractableDataCollection.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/HandGrab/HandGrabInteraction.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/HandGrab/HandGrabInteractor.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/HandGrab/HandGrabPose.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/HandGrab/HandGrabResult.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/HandGrab/HandGrabTarget.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/HandGrab/HandGrabUtils.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/HandGrab/HandPose.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/HandGrab/IHandGrabInteractable.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/HandGrab/IHandGrabInteractor.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/HandGrab/IHandGrabState.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/HandGrab/PoseMeasureParameters.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/CapsuleLocomotionHandler.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/CharacterController.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/FirstPersonLocomotor.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/FlyingLocomotor.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/ILocomotionEventBroadcaster.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/ILocomotionEventHandler.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/LocomotionActiveState.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/LocomotionEvent.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/LocomotionEventsConnection.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/LocomotionGate.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/PlayerLocomotor.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/Slide/SlideLocomotionBroadcaster.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/Slide/StepLocomotionBroadcaster.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/Teleport/TeleportArcGravity.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/Teleport/TeleportCandidateComputer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/Teleport/TeleportInteractable.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/Teleport/TeleportInteractor.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/Teleport/Visuals/ActiveStateFingerVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/Teleport/Visuals/PinchPointerVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/Teleport/Visuals/ReticleDataTeleport.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/Teleport/Visuals/TeleportArcVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/Teleport/Visuals/TeleportProceduralArcVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/Teleport/Visuals/TeleportReticleDrawer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/Tunneling/LocomotionTunneling.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/Tunneling/TunnelingEffect.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/Tunneling/WallPenetrationTunneling.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/Turn/AnimatedSnapTurnVisuals.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/Turn/LocomotionAxisTurnerInteractor.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/Turn/LocomotionTurnerInteractor.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/Turn/LocomotionTurnerInteractorEventsWrapper.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/Turn/LocomotionTurnerInteractorVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/Turn/TurnArrowVisuals.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/Turn/TurnerEventBroadcaster.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/Turn/TurnLocomotionBroadcaster.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Locomotion/Visuals/LocomotionGateUnityEventWrapper.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Poke/PokeInteractable.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Poke/PokeInteractor.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Poke/Visuals/HandPokeLimiterVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Poke/Visuals/PokeInteractableVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Ray/RayInteractable.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Ray/RayInteractor.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Ray/Visuals/ControllerRayVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Ray/Visuals/HandRayInteractorCursorVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Ray/Visuals/RayInteractorCursorVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Ray/Visuals/RayInteractorDebugGizmos.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Ray/Visuals/RayInteractorDebugPolylineGizmos.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Ray/Visuals/RayInteractorPinchVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Snap/ISnapPoseDelegate.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Snap/ListLayout.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Snap/ListSnapPoseDelegate.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Snap/SequentialSlotsProvider.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Snap/SnapInteractable.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Snap/SnapInteractor.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Snap/SurfaceSnapPoseDelegate.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/Snap/Visuals/SnapInteractorFollowVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/TouchHandGrab/HandSphereMap.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/TouchHandGrab/HandSphereMapGenerator.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/TouchHandGrab/IHandSphereMap.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/TouchHandGrab/TouchHandGrabInteractable.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/TouchHandGrab/TouchHandGrabInteractor.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/TouchHandGrab/TouchHandGrabInteractorVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/TouchHandGrab/TouchShadowHand.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/UseGrab/Axis1DFingerUseAPI.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/UseGrab/HandGrabUseInteractable.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/UseGrab/HandGrabUseInteractor.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/UseGrab/IFingerUseAPI.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/UseGrab/IHandGrabUseDelegate.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/UseGrab/UseFingerControllerAPI.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/UseGrab/UseFingerCurlAPI.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Models/UseGrab/UseFingerRawPinchAPI.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Pointable/IPointable.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Pointable/IPointableCanvas.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Pointable/PointableDebugGizmos.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Pointable/PointableDebugPolylineGizmos.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Pointable/PointableDebugVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Pointable/PointableElement.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Pointable/PointerInteractable.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Pointable/PointerInteractor.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Pointable/VirtualPointable.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Surfaces/AxisAlignedBox.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Surfaces/BoundsClipper.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Surfaces/CircleSurface.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Surfaces/ClippedCylinderSurface.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Surfaces/ClippedPlaneSurface.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Surfaces/ColliderSurface.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Surfaces/CylinderClipper.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Surfaces/CylinderSurface.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Surfaces/IBounds.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Surfaces/IClippedSurface.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Surfaces/IPolyline.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Surfaces/ISurface.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Surfaces/ISurfaceClipper.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Surfaces/ISurfacePatch.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Surfaces/NavMeshSurface.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Surfaces/PhysicsLayerSurface.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Surfaces/PlaneSurface.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Visuals/BecomeChildOfTargetOnStart.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Visuals/DebugTree.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Visuals/DebugTreeUI.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Visuals/HandConfidenceVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Visuals/HandDebugGizmos.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Visuals/HandVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Visuals/IHandVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Visuals/InteractableColorVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Visuals/InteractableDebugVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Visuals/InteractorDebugVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Visuals/InteractorGroupDebugTreeUI.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Visuals/InteractorGroupNodeUI.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Interaction/Visuals/SelectorDebugVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/MaterialProperties/DotGridProperties.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/MaterialProperties/MaterialPropertyBlockEditor.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/MaterialProperties/RoundedBoxProperties.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Movements/AutoMoveTowardsTargetProvider.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Movements/FollowTargetProvider.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Movements/IMovement.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Movements/IMovementProvider.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Movements/JoystickPoseMovementProvider.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Movements/MoveAtSourceProvider.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Movements/MoveFromTargetProvider.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Movements/MoveTowardsTargetProvider.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Movements/ObjectPullProvider.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Movements/PoseTravelData.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Polyline/DebugGizmos.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Polyline/PolylineGizmos.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Polyline/PolylineRenderer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Polyline/TransformsPolyline.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/ColliderContainsHandJointActiveState.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/Debug/ActiveStateDebugTree.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/Debug/ActiveStateDebugTreeUI.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/Debug/ActiveStateDebugVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/Debug/ActiveStateModel.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/Debug/ActiveStateNodeUIHorizontal.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/Debug/ActiveStateNodeUIVertical.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/Debug/FingerFeatureDebugVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/Debug/FingerFeatureSkeletalDebugVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/Debug/HandShapeDebugVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/Debug/HandShapeSkeletalDebugVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/Debug/JointRotationDebugVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/Debug/JointVelocityDebugVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/Debug/TransformFeatureDebugVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/Debug/TransformFeatureVectorDebugParentVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/Debug/TransformFeatureVectorDebugVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/Debug/TransformRecognizerDebugVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/FeatureConfigBuilder.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/FeatureDescription.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/FeatureStateProvider.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/FingerFeatureProperties.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/FingerFeatureStateProvider.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/FingerFeatureStateProviderRef.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/FingerFeatureStateThresholds.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/FingerShapes.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/HmdOffset.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/IFeatureStateThreshold.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/IFeatureStateThresholds.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/IFeatureThresholds.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/JointDeltaProvider.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/JointDeltaProviderRef.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/JointDistanceActiveState.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/JointRotationActiveState.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/JointVelocityActiveState.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/Sequence.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/SequenceActiveState.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/ShapeRecognizer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/ShapeRecognizerActiveState.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/TransformFeatureProperties.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/TransformFeatureStateProvider.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/TransformFeatureStateProviderRef.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/TransformFeatureStateThresholds.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/TransformFeatureValueProvider.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/PoseDetection/TransformRecognizerActiveState.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Selection/Controllers/ControllerOffset.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Selection/Controllers/ControllerPointerPose.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Selection/Controllers/ControllerSelector.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Selection/Hands/CenterEyeOffset.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Selection/Hands/ConicalFrustum.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Selection/Hands/FingerPinchValue.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Selection/Hands/HandJoint.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Selection/Hands/HandJointsPose.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Selection/Hands/HandPinchOffset.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Selection/Hands/HandPointerPose.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Selection/Hands/HandRootOffset.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Selection/Hands/HandTransformScaler.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Selection/Hands/IndexPinchSafeReleaseSelector.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Selection/Hands/IndexPinchSelector.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Selection/Hands/ShoulderEstimatePosition.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Selection/Hands/WristAngleActiveState.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Selection/Virtual/VirtualSelector.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Shapes/Cylinder.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Shapes/CylinderOrientation.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Shapes/CylinderSegment.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Shapes/ICurvedPlane.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Throw/ControllerPoseInputDevice.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Throw/HandPoseInputDevice.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Throw/HandTrackingConfidenceProvider.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Throw/IPoseInputDevice.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Throw/IThrowVelocityCalculator.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Throw/IVelocityCalculator.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Throw/RANSACVelocity.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Throw/RANSACVelocityCalculator.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Throw/StandardVelocityCalculator.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Unity/ActiveStateUnityEventWrapper.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Unity/Input/InputAxis.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Unity/Input/InputButton.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Unity/Input/InputKey.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Unity/Input/InputMouseButton.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Unity/InteractableUnityEventWrapper.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Unity/InteractorUnityEventWrapper.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Unity/PhysicsGrabbable.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Unity/PointableCanvas.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Unity/PointableCanvasMesh.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Unity/PointableCanvasModule.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Unity/PointableCanvasUnityEventWrapper.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Unity/PointableUnityEventWrapper.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Unity/RectTransformBoundsClipperDriver.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Unity/RigidbodyKinematicLocker.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Unity/SelectorUnityEventWrapper.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Unity/ToggleDeselect.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Unity/UnityCanvas/CanvasCylinder.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Unity/UnityCanvas/CanvasMesh.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Unity/UnityCanvas/CanvasMeshRenderer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Unity/UnityCanvas/CanvasRect.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Unity/UnityCanvas/CanvasRenderTexture.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Unity/UnityCanvas/RenderingMode.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Unity/UnityCanvas/UpdateCanvasSortingOrder.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Unity/UnityInfo.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Utils/AssertUtils.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Utils/BoundsExtensions.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Utils/ConeUtils.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Utils/Context.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Utils/DecorationUtils.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Utils/DeprecatedPrefab.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Utils/FinalAction.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Utils/ITimeConsumer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Utils/MonoBehaviourEndOfFrameExtensions.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Utils/NativeMethods.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Utils/PoseUtils.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Utils/ProgressCurve.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Utils/RandomSampleConsensus.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Utils/RingBuffer.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Utils/SurfaceUtils.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Utils/TransformerUtils.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/Scripts/Utils/VersionTextVisual.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/ThirdParty/InterfaceSupport/InterfaceAttribute.cs"
"Library/PackageCache/com.meta.xr.sdk.interaction@e52ba4dfd787/Runtime/ThirdParty/LeapMotion/VelocityCalculatorUtilMethods.cs"
-langversion:9.0
/unsafe+
/deterministic
/optimize-
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
-warn:0
/additionalfile:"Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.UnityAdditionalFile.txt"