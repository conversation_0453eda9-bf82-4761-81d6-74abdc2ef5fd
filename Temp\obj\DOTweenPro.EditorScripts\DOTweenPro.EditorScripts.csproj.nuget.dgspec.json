{"format": 1, "restore": {"D:\\Projects\\VRTesting\\DOTweenPro.EditorScripts.csproj": {}}, "projects": {"D:\\Projects\\VRTesting\\DOTween.Modules.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VRTesting\\DOTween.Modules.csproj", "projectName": "DOTween.Modules", "projectPath": "D:\\Projects\\VRTesting\\DOTween.Modules.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VRTesting\\Temp\\obj\\DOTween.Modules\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Projects\\VRTesting\\UnityEditor.UI.csproj": {"projectPath": "D:\\Projects\\VRTesting\\UnityEditor.UI.csproj"}, "D:\\Projects\\VRTesting\\UnityEngine.UI.csproj": {"projectPath": "D:\\Projects\\VRTesting\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}}}, "D:\\Projects\\VRTesting\\DOTweenPro.EditorScripts.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VRTesting\\DOTweenPro.EditorScripts.csproj", "projectName": "DOTweenPro.EditorScripts", "projectPath": "D:\\Projects\\VRTesting\\DOTweenPro.EditorScripts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VRTesting\\Temp\\obj\\DOTweenPro.EditorScripts\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Projects\\VRTesting\\DOTween.Modules.csproj": {"projectPath": "D:\\Projects\\VRTesting\\DOTween.Modules.csproj"}, "D:\\Projects\\VRTesting\\DOTweenPro.Scripts.csproj": {"projectPath": "D:\\Projects\\VRTesting\\DOTweenPro.Scripts.csproj"}, "D:\\Projects\\VRTesting\\Unity.TextMeshPro.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.TextMeshPro.csproj"}, "D:\\Projects\\VRTesting\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Projects\\VRTesting\\UnityEditor.TestRunner.csproj"}, "D:\\Projects\\VRTesting\\UnityEditor.UI.csproj": {"projectPath": "D:\\Projects\\VRTesting\\UnityEditor.UI.csproj"}, "D:\\Projects\\VRTesting\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Projects\\VRTesting\\UnityEngine.TestRunner.csproj"}, "D:\\Projects\\VRTesting\\UnityEngine.UI.csproj": {"projectPath": "D:\\Projects\\VRTesting\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}}}, "D:\\Projects\\VRTesting\\DOTweenPro.Scripts.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VRTesting\\DOTweenPro.Scripts.csproj", "projectName": "DOTweenPro.Scripts", "projectPath": "D:\\Projects\\VRTesting\\DOTweenPro.Scripts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VRTesting\\Temp\\obj\\DOTweenPro.Scripts\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Projects\\VRTesting\\DOTween.Modules.csproj": {"projectPath": "D:\\Projects\\VRTesting\\DOTween.Modules.csproj"}, "D:\\Projects\\VRTesting\\Unity.TextMeshPro.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.TextMeshPro.csproj"}, "D:\\Projects\\VRTesting\\UnityEditor.UI.csproj": {"projectPath": "D:\\Projects\\VRTesting\\UnityEditor.UI.csproj"}, "D:\\Projects\\VRTesting\\UnityEngine.UI.csproj": {"projectPath": "D:\\Projects\\VRTesting\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}}}, "D:\\Projects\\VRTesting\\Unity.TextMeshPro.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VRTesting\\Unity.TextMeshPro.csproj", "projectName": "Unity.TextMeshPro", "projectPath": "D:\\Projects\\VRTesting\\Unity.TextMeshPro.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VRTesting\\Temp\\obj\\Unity.TextMeshPro\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Projects\\VRTesting\\UnityEditor.UI.csproj": {"projectPath": "D:\\Projects\\VRTesting\\UnityEditor.UI.csproj"}, "D:\\Projects\\VRTesting\\UnityEngine.UI.csproj": {"projectPath": "D:\\Projects\\VRTesting\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}}}, "D:\\Projects\\VRTesting\\UnityEditor.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VRTesting\\UnityEditor.TestRunner.csproj", "projectName": "UnityEditor.TestRunner", "projectPath": "D:\\Projects\\VRTesting\\UnityEditor.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VRTesting\\Temp\\obj\\UnityEditor.TestRunner\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Projects\\VRTesting\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Projects\\VRTesting\\UnityEngine.TestRunner.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}}}, "D:\\Projects\\VRTesting\\UnityEditor.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VRTesting\\UnityEditor.UI.csproj", "projectName": "UnityEditor.UI", "projectPath": "D:\\Projects\\VRTesting\\UnityEditor.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VRTesting\\Temp\\obj\\UnityEditor.UI\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Projects\\VRTesting\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Projects\\VRTesting\\UnityEditor.TestRunner.csproj"}, "D:\\Projects\\VRTesting\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Projects\\VRTesting\\UnityEngine.TestRunner.csproj"}, "D:\\Projects\\VRTesting\\UnityEngine.UI.csproj": {"projectPath": "D:\\Projects\\VRTesting\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}}}, "D:\\Projects\\VRTesting\\UnityEngine.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VRTesting\\UnityEngine.TestRunner.csproj", "projectName": "UnityEngine.TestRunner", "projectPath": "D:\\Projects\\VRTesting\\UnityEngine.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VRTesting\\Temp\\obj\\UnityEngine.TestRunner\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}}}, "D:\\Projects\\VRTesting\\UnityEngine.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VRTesting\\UnityEngine.UI.csproj", "projectName": "UnityEngine.UI", "projectPath": "D:\\Projects\\VRTesting\\UnityEngine.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VRTesting\\Temp\\obj\\UnityEngine.UI\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}}}}}