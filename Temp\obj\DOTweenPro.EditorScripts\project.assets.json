{"version": 3, "targets": {".NETStandard,Version=v2.1": {"DOTween.Modules/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/DOTween.Modules.dll": {}}, "runtime": {"bin/placeholder/DOTween.Modules.dll": {}}}, "DOTweenPro.Scripts/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"DOTween.Modules": "1.0.0", "Unity.TextMeshPro": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/DOTweenPro.Scripts.dll": {}}, "runtime": {"bin/placeholder/DOTweenPro.Scripts.dll": {}}}, "Unity.TextMeshPro/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.TextMeshPro.dll": {}}, "runtime": {"bin/placeholder/Unity.TextMeshPro.dll": {}}}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEngine.TestRunner": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}}, "UnityEditor.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.UI.dll": {}}}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}}, "UnityEngine.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.UI.dll": {}}}}}, "libraries": {"DOTween.Modules/1.0.0": {"type": "project", "path": "DOTween.Modules.csproj", "msbuildProject": "DOTween.Modules.csproj"}, "DOTweenPro.Scripts/1.0.0": {"type": "project", "path": "DOTweenPro.Scripts.csproj", "msbuildProject": "DOTweenPro.Scripts.csproj"}, "Unity.TextMeshPro/1.0.0": {"type": "project", "path": "Unity.TextMeshPro.csproj", "msbuildProject": "Unity.TextMeshPro.csproj"}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "path": "UnityEditor.TestRunner.csproj", "msbuildProject": "UnityEditor.TestRunner.csproj"}, "UnityEditor.UI/1.0.0": {"type": "project", "path": "UnityEditor.UI.csproj", "msbuildProject": "UnityEditor.UI.csproj"}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "path": "UnityEngine.TestRunner.csproj", "msbuildProject": "UnityEngine.TestRunner.csproj"}, "UnityEngine.UI/1.0.0": {"type": "project", "path": "UnityEngine.UI.csproj", "msbuildProject": "UnityEngine.UI.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["DOTween.Modules >= 1.0.0", "DOTweenPro.Scripts >= 1.0.0", "Unity.TextMeshPro >= 1.0.0", "UnityEditor.TestRunner >= 1.0.0", "UnityEditor.UI >= 1.0.0", "UnityEngine.TestRunner >= 1.0.0", "UnityEngine.UI >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VRTesting\\DOTweenPro.EditorScripts.csproj", "projectName": "DOTweenPro.EditorScripts", "projectPath": "D:\\Projects\\VRTesting\\DOTweenPro.EditorScripts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VRTesting\\Temp\\obj\\DOTweenPro.EditorScripts\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Projects\\VRTesting\\DOTween.Modules.csproj": {"projectPath": "D:\\Projects\\VRTesting\\DOTween.Modules.csproj"}, "D:\\Projects\\VRTesting\\DOTweenPro.Scripts.csproj": {"projectPath": "D:\\Projects\\VRTesting\\DOTweenPro.Scripts.csproj"}, "D:\\Projects\\VRTesting\\Unity.TextMeshPro.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.TextMeshPro.csproj"}, "D:\\Projects\\VRTesting\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Projects\\VRTesting\\UnityEditor.TestRunner.csproj"}, "D:\\Projects\\VRTesting\\UnityEditor.UI.csproj": {"projectPath": "D:\\Projects\\VRTesting\\UnityEditor.UI.csproj"}, "D:\\Projects\\VRTesting\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Projects\\VRTesting\\UnityEngine.TestRunner.csproj"}, "D:\\Projects\\VRTesting\\UnityEngine.UI.csproj": {"projectPath": "D:\\Projects\\VRTesting\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}}}}