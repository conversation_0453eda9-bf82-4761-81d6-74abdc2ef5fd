{"Bee.Core.BuildProgramContext+BuildProgramContextOutputData": {"MaxRerunAllowed": 2147483647}, "ScriptCompilationBuildProgram.Data.ScriptCompilationData_Out": {"Assemblies": [{"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/AssistantCoreSDKRuntime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/AssistantCoreSDKRuntime.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/AssistantCoreSDKRuntime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/DOTween.Modules.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/DOTween.Modules.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/DOTween.Modules.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Net.endel.nativewebsocket.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Net.endel.nativewebsocket.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Net.endel.nativewebsocket.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Voice.Hub.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Voice.Hub.Runtime.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Voice.Hub.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Voice.NLayer.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Voice.NLayer.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Voice.NLayer.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Voice.Opus.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Voice.Opus.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Voice.Opus.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Audio.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Audio.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Audio.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.Callbacks.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.Callbacks.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.Callbacks.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.Id.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.Id.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.Id.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.Reflection.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.Reflection.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.Reflection.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.ImmersiveDebugger.Interface.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.ImmersiveDebugger.Interface.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.ImmersiveDebugger.Interface.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/NaughtyAttributes.Core.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/NaughtyAttributes.Core.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/NaughtyAttributes.Core.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Platform.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Platform.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Platform.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/RootMotion.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/RootMotion.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/RootMotion.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AI.Navigation.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AI.Navigation.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AI.Navigation.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Multiplayer.Center.Common.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PerformanceTesting.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PerformanceTesting.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PerformanceTesting.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Utilities.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.Management.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.Management.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.Management.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.SpatialTracking.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.SpatialTracking.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.SpatialTracking.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/VoiceSDK.Telemetry.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/VoiceSDK.Telemetry.Runtime.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/VoiceSDK.Telemetry.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/AssistantCoreSDKEditor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/AssistantCoreSDKEditor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/AssistantCoreSDKEditor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/AssistantVoiceCommandCommon.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/AssistantVoiceCommandCommon.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/AssistantVoiceCommandCommon.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/DOTweenPro.Scripts.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/DOTweenPro.Scripts.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/DOTweenPro.Scripts.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Audio.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Audio.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Audio.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/NaughtyAttributes.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/NaughtyAttributes.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/NaughtyAttributes.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Platform.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Platform.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Platform.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/RootMotionEditor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/RootMotionEditor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/RootMotionEditor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AI.Navigation.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AI.Navigation.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.CodeGen.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.CodeGen.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.ForUI.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.ForUI.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.ForUI.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.TestFramework.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.TestFramework.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Multiplayer.Center.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PerformanceTesting.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.CoreUtils.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.CoreUtils.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.CoreUtils.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.Oculus.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.Oculus.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.Oculus.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.SpatialTracking.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.SpatialTracking.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.SpatialTracking.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.XR.LegacyInputHelpers.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.XR.LegacyInputHelpers.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.XR.LegacyInputHelpers.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/AssistantVoiceCommandCommon.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/AssistantVoiceCommandCommon.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/AssistantVoiceCommandCommon.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/AutoHandAssembly.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/AutoHandAssembly.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/AutoHandAssembly.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/DOTweenPro.EditorScripts.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/DOTweenPro.EditorScripts.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/DOTweenPro.EditorScripts.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.InterfaceSupport.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.InterfaceSupport.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.InterfaceSupport.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.Samples.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.Samples.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.Samples.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.UnityXR.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.UnityXR.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.UnityXR.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AI.Navigation.Updater.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AI.Navigation.Updater.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.CodeGen.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.CodeGen.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.CodeGen.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.CoreUtils.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.CoreUtils.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.CoreUtils.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/VoiceSDK.Telemetry.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/VoiceSDK.Telemetry.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/VoiceSDK.Telemetry.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Autohand.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Autohand.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Autohand.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Autohand.XR.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Autohand.XR.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Autohand.XR.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.WitAi.Lib.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.WitAi.Lib.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.WitAi.Lib.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/NewAssembly.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/NewAssembly.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/NewAssembly.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.Editor.UnityXR.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.Editor.UnityXR.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.Editor.UnityXR.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.Samples.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.Samples.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.Samples.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.Management.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.Management.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.Management.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Autohand.XR.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Autohand.XR.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Autohand.XR.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.VoiceSDK.Mic.Common.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.VoiceSDK.Mic.Common.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.VoiceSDK.Mic.Common.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.Oculus.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.Oculus.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.Oculus.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.VoiceSDK.Mic.Other.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.VoiceSDK.Mic.Other.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.VoiceSDK.Mic.Other.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.VoiceSDK.Mic.WebGL.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.VoiceSDK.Mic.WebGL.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.VoiceSDK.Mic.WebGL.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.XR.LegacyInputHelpers.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.XR.LegacyInputHelpers.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.XR.LegacyInputHelpers.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.WitAi.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.WitAi.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.WitAi.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.Features.ConformanceAutomation.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.Features.ConformanceAutomation.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.Features.ConformanceAutomation.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.Features.MetaQuestSupport.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.Features.MetaQuestSupport.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.Features.MetaQuestSupport.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Wit.Dictation.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Wit.Dictation.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Wit.Dictation.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.WitAI.Lib.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.WitAI.Lib.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.WitAI.Lib.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.WitAi.TTS.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.WitAi.TTS.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.WitAi.TTS.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.VR.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.VR.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.VR.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/PPv2URPConverters.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/PPv2URPConverters.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/PPv2URPConverters.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.Features.MetaQuestSupport.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.Features.MetaQuestSupport.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.Features.MetaQuestSupport.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.Features.MockRuntime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.Features.MockRuntime.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.Features.MockRuntime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.Features.OculusQuestSupport.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.Features.OculusQuestSupport.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.Features.OculusQuestSupport.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.Features.RuntimeDebugger.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.Features.RuntimeDebugger.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.Features.RuntimeDebugger.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Wit.Composer.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Wit.Composer.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Wit.Composer.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.WitAi.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.WitAi.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.WitAi.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.BuildingBlocks.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.BuildingBlocks.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.BuildingBlocks.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.UserInterface.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.UserInterface.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.UserInterface.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.EnvironmentDepth.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.EnvironmentDepth.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.EnvironmentDepth.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.ImmersiveDebugger.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.ImmersiveDebugger.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.ImmersiveDebugger.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Haptics.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Haptics.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Haptics.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.OVR.Samples.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.OVR.Samples.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.OVR.Samples.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.OVR.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.OVR.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.OVR.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.Features.OculusQuestSupport.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.Features.OculusQuestSupport.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.Features.OculusQuestSupport.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.Features.RuntimeDebugger.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.Features.RuntimeDebugger.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Unity.XR.OpenXR.Features.RuntimeDebugger.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/VoiceSDK.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/VoiceSDK.Runtime.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/VoiceSDK.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Voice.Hub.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Voice.Hub.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Voice.Hub.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Wit.Composer.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Wit.Composer.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Wit.Composer.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Wit.Dictation.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Wit.Dictation.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Wit.Dictation.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.WitAi.TTS.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.WitAi.TTS.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.WitAi.TTS.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.BuildingBlocks.DepthAPI.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.BuildingBlocks.DepthAPI.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.BuildingBlocks.DepthAPI.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.Settings.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.Settings.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.Settings.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Haptics.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Haptics.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Haptics.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/VoiceSDK.Dictation.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/VoiceSDK.Dictation.Runtime.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/VoiceSDK.Dictation.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/VoiceSDK.Runtime.Composer.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/VoiceSDK.Runtime.Composer.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/VoiceSDK.Runtime.Composer.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Voice.VSDKHub.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Voice.VSDKHub.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.Voice.VSDKHub.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.Tags.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.Tags.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.Tags.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/VoiceSDK.Dictation.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/VoiceSDK.Dictation.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/VoiceSDK.Dictation.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/VoiceSDK.Editor.Composer.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/VoiceSDK.Editor.Composer.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/VoiceSDK.Editor.Composer.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.ToolingSupport.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.ToolingSupport.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.ToolingSupport.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/VoiceSDK.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/VoiceSDK.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/VoiceSDK.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.StatusMenu.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.StatusMenu.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.StatusMenu.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Guides.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Guides.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Guides.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.VR.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.VR.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.VR.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.Notifications.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.Notifications.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.Notifications.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.PlayCompanion.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.PlayCompanion.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.PlayCompanion.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.EnvironmentDepth.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.EnvironmentDepth.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.EnvironmentDepth.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.ImmersiveDebugger.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.ImmersiveDebugger.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.ImmersiveDebugger.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.VR.Scripts.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.VR.Scripts.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.VR.Scripts.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/meta.xr.mrutilitykit.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/meta.xr.mrutilitykit.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/meta.xr.mrutilitykit.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Autohand.MetaXR.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Autohand.MetaXR.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Autohand.MetaXR.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.TelemetryUI.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.TelemetryUI.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.TelemetryUI.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.UPST.Notifications.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.UPST.Notifications.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.UPST.Notifications.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.MultiplayerBlocks.Shared.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.MultiplayerBlocks.Shared.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.MultiplayerBlocks.Shared.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/MetaXrSimulator.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/MetaXrSimulator.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/MetaXrSimulator.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Autohand.MetaXR.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Autohand.MetaXR.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Autohand.MetaXR.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.BuildingBlocks.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.BuildingBlocks.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.BuildingBlocks.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.BuildingBlocks.DepthAPI.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.BuildingBlocks.DepthAPI.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.BuildingBlocks.DepthAPI.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.Guide.About.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.Guide.About.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.Editor.Guide.About.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.OVR.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.OVR.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Oculus.Interaction.OVR.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.MultiplayerBlocks.Shared.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.MultiplayerBlocks.Shared.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.MultiplayerBlocks.Shared.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/meta.xr.mrutilitykit.editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/meta.xr.mrutilitykit.editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/meta.xr.mrutilitykit.editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.MultiplayerBlocks.Fusion.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.MultiplayerBlocks.Fusion.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.MultiplayerBlocks.Fusion.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.MultiplayerBlocks.NGO.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.MultiplayerBlocks.NGO.Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Meta.XR.MultiplayerBlocks.NGO.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.rsp", "MovedFromExtractorFile": "D:/Projects/VRTesting/Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm"}], "LocalizeCompilerMessages": false}}