{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Assembly-CSharp-firstpass/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"AssistantCoreSDKEditor": "1.0.0", "AssistantCoreSDKRuntime": "1.0.0", "AssistantVoiceCommandCommon": "1.0.0", "AssistantVoiceCommandCommon.Editor": "1.0.0", "AutoHandAssembly": "1.0.0", "Autohand.Editor": "1.0.0", "Autohand.MetaXR": "1.0.0", "Autohand.MetaXR.Editor": "1.0.0", "Autohand.XR": "1.0.0", "Autohand.XR.Editor": "1.0.0", "Meta.Net.endel.nativewebsocket": "1.0.0", "Meta.Voice.Hub.Editor": "1.0.0", "Meta.Voice.Hub.Runtime": "1.0.0", "Meta.Voice.NLayer": "1.0.0", "Meta.Voice.Opus": "1.0.0", "Meta.Voice.VSDKHub.Editor": "1.0.0", "Meta.VoiceSDK.Mic.Common": "1.0.0", "Meta.VoiceSDK.Mic.Other": "1.0.0", "Meta.VoiceSDK.Mic.WebGL": "1.0.0", "Meta.Wit.Composer": "1.0.0", "Meta.Wit.Composer.Editor": "1.0.0", "Meta.Wit.Dictation": "1.0.0", "Meta.Wit.Dictation.Editor": "1.0.0", "Meta.WitAI.Lib.Editor": "1.0.0", "Meta.WitAi": "1.0.0", "Meta.WitAi.Editor": "1.0.0", "Meta.WitAi.Lib": "1.0.0", "Meta.WitAi.TTS": "1.0.0", "Meta.WitAi.TTS.Editor": "1.0.0", "Meta.XR.Audio": "1.0.0", "Meta.XR.Audio.Editor": "1.0.0", "Meta.XR.BuildingBlocks": "1.0.0", "Meta.XR.BuildingBlocks.DepthAPI": "1.0.0", "Meta.XR.BuildingBlocks.DepthAPI.Editor": "1.0.0", "Meta.XR.BuildingBlocks.Editor": "1.0.0", "Meta.XR.Editor.Callbacks": "1.0.0", "Meta.XR.Editor.Guide.About": "1.0.0", "Meta.XR.Editor.Id": "1.0.0", "Meta.XR.Editor.Notifications": "1.0.0", "Meta.XR.Editor.PlayCompanion": "1.0.0", "Meta.XR.Editor.Reflection": "1.0.0", "Meta.XR.Editor.Settings": "1.0.0", "Meta.XR.Editor.StatusMenu": "1.0.0", "Meta.XR.Editor.Tags": "1.0.0", "Meta.XR.Editor.TelemetryUI": "1.0.0", "Meta.XR.Editor.ToolingSupport": "1.0.0", "Meta.XR.Editor.UPST.Notifications": "1.0.0", "Meta.XR.Editor.UserInterface": "1.0.0", "Meta.XR.EnvironmentDepth": "1.0.0", "Meta.XR.EnvironmentDepth.Editor": "1.0.0", "Meta.XR.Guides.Editor": "1.0.0", "Meta.XR.ImmersiveDebugger": "1.0.0", "Meta.XR.ImmersiveDebugger.Editor": "1.0.0", "Meta.XR.ImmersiveDebugger.Interface": "1.0.0", "Meta.XR.MultiplayerBlocks.Fusion.Editor": "1.0.0", "Meta.XR.MultiplayerBlocks.NGO.Editor": "1.0.0", "Meta.XR.MultiplayerBlocks.Shared": "1.0.0", "Meta.XR.MultiplayerBlocks.Shared.Editor": "1.0.0", "MetaXrSimulator.Editor": "1.0.0", "NaughtyAttributes.Core": "1.0.0", "NaughtyAttributes.Editor": "1.0.0", "NewAssembly": "1.0.0", "Oculus.Haptics": "1.0.0", "Oculus.Haptics.Editor": "1.0.0", "Oculus.Interaction": "1.0.0", "Oculus.Interaction.Editor": "1.0.0", "Oculus.Interaction.Editor.UnityXR": "1.0.0", "Oculus.Interaction.InterfaceSupport": "1.0.0", "Oculus.Interaction.OVR": "1.0.0", "Oculus.Interaction.OVR.Editor": "1.0.0", "Oculus.Interaction.OVR.Samples": "1.0.0", "Oculus.Interaction.Samples": "1.0.0", "Oculus.Interaction.Samples.Editor": "1.0.0", "Oculus.Interaction.UnityXR": "1.0.0", "Oculus.Platform": "1.0.0", "Oculus.Platform.Editor": "1.0.0", "Oculus.VR": "1.0.0", "Oculus.VR.Editor": "1.0.0", "Oculus.VR.Scripts.Editor": "1.0.0", "PPv2URPConverters": "1.0.0", "RootMotion": "1.0.0", "RootMotionEditor": "1.0.0", "Unity.AI.Navigation": "1.0.0", "Unity.AI.Navigation.Editor": "1.0.0", "Unity.AI.Navigation.Editor.ConversionSystem": "1.0.0", "Unity.AI.Navigation.Updater": "1.0.0", "Unity.Burst": "1.0.0", "Unity.Burst.Editor": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Collections.Editor": "1.0.0", "Unity.InputSystem": "1.0.0", "Unity.InputSystem.ForUI": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.Mathematics.Editor": "1.0.0", "Unity.Multiplayer.Center.Common": "1.0.0", "Unity.Multiplayer.Center.Editor": "1.0.0", "Unity.PlasticSCM.Editor": "1.0.0", "Unity.RenderPipeline.Universal.ShaderLibrary": "1.0.0", "Unity.RenderPipelines.Core.Editor": "1.0.0", "Unity.RenderPipelines.Core.Editor.Shared": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Core.Runtime.Shared": "1.0.0", "Unity.RenderPipelines.Core.ShaderLibrary": "1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime": "1.0.0", "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary": "1.0.0", "Unity.RenderPipelines.Universal.2D.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Config.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Editor": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Shaders": "1.0.0", "Unity.Rendering.LightTransport.Editor": "1.0.0", "Unity.Rendering.LightTransport.Runtime": "1.0.0", "Unity.Rider.Editor": "1.0.0", "Unity.Searcher.Editor": "1.0.0", "Unity.ShaderGraph.Editor": "1.0.0", "Unity.TextMeshPro": "1.0.0", "Unity.TextMeshPro.Editor": "1.0.0", "Unity.Timeline": "1.0.0", "Unity.Timeline.Editor": "1.0.0", "Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Core.Editor": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "Unity.VisualScripting.Flow.Editor": "1.0.0", "Unity.VisualScripting.SettingsProvider.Editor": "1.0.0", "Unity.VisualScripting.Shared.Editor": "1.0.0", "Unity.VisualScripting.State": "1.0.0", "Unity.VisualScripting.State.Editor": "1.0.0", "Unity.VisualStudio.Editor": "1.0.0", "Unity.XR.CoreUtils": "1.0.0", "Unity.XR.CoreUtils.Editor": "1.0.0", "Unity.XR.Management": "1.0.0", "Unity.XR.Management.Editor": "1.0.0", "Unity.XR.Oculus": "1.0.0", "Unity.XR.Oculus.Editor": "1.0.0", "Unity.XR.OpenXR": "1.0.0", "Unity.XR.OpenXR.Editor": "1.0.0", "Unity.XR.OpenXR.Features.ConformanceAutomation": "1.0.0", "Unity.XR.OpenXR.Features.MetaQuestSupport": "1.0.0", "Unity.XR.OpenXR.Features.MetaQuestSupport.Editor": "1.0.0", "Unity.XR.OpenXR.Features.OculusQuestSupport": "1.0.0", "Unity.XR.OpenXR.Features.OculusQuestSupport.Editor": "1.0.0", "Unity.XR.OpenXR.Features.RuntimeDebugger.Editor": "1.0.0", "UnityEditor.SpatialTracking": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEditor.XR.LegacyInputHelpers": "1.0.0", "UnityEngine.SpatialTracking": "1.0.0", "UnityEngine.UI": "1.0.0", "UnityEngine.XR.LegacyInputHelpers": "1.0.0", "VoiceSDK.Dictation.Editor": "1.0.0", "VoiceSDK.Dictation.Runtime": "1.0.0", "VoiceSDK.Editor": "1.0.0", "VoiceSDK.Editor.Composer": "1.0.0", "VoiceSDK.Runtime": "1.0.0", "VoiceSDK.Runtime.Composer": "1.0.0", "VoiceSDK.Telemetry": "1.0.0", "VoiceSDK.Telemetry.Runtime": "1.0.0", "meta.xr.mrutilitykit": "1.0.0", "meta.xr.mrutilitykit.editor": "1.0.0"}, "compile": {"bin/placeholder/Assembly-CSharp-firstpass.dll": {}}, "runtime": {"bin/placeholder/Assembly-CSharp-firstpass.dll": {}}}, "AssistantCoreSDKEditor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"AssistantCoreSDKRuntime": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/AssistantCoreSDKEditor.dll": {}}, "runtime": {"bin/placeholder/AssistantCoreSDKEditor.dll": {}}}, "AssistantCoreSDKRuntime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/AssistantCoreSDKRuntime.dll": {}}, "runtime": {"bin/placeholder/AssistantCoreSDKRuntime.dll": {}}}, "AssistantVoiceCommandCommon/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"AssistantCoreSDKRuntime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/AssistantVoiceCommandCommon.dll": {}}, "runtime": {"bin/placeholder/AssistantVoiceCommandCommon.dll": {}}}, "AssistantVoiceCommandCommon.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"AssistantVoiceCommandCommon": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/AssistantVoiceCommandCommon.Editor.dll": {}}, "runtime": {"bin/placeholder/AssistantVoiceCommandCommon.Editor.dll": {}}}, "Autohand.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"AutoHandAssembly": "1.0.0", "NaughtyAttributes.Core": "1.0.0", "NaughtyAttributes.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Autohand.Editor.dll": {}}, "runtime": {"bin/placeholder/Autohand.Editor.dll": {}}}, "Autohand.MetaXR/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"AutoHandAssembly": "1.0.0", "Autohand.XR": "1.0.0", "Oculus.Interaction": "1.0.0", "Oculus.VR": "1.0.0", "Unity.InputSystem": "1.0.0", "Unity.TextMeshPro": "1.0.0", "Unity.XR.CoreUtils": "1.0.0", "Unity.XR.Management": "1.0.0", "Unity.XR.OpenXR": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0", "meta.xr.mrutilitykit": "1.0.0"}, "compile": {"bin/placeholder/Autohand.MetaXR.dll": {}}, "runtime": {"bin/placeholder/Autohand.MetaXR.dll": {}}}, "Autohand.MetaXR.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"AutoHandAssembly": "1.0.0", "Autohand.MetaXR": "1.0.0", "Autohand.XR": "1.0.0", "Unity.InputSystem": "1.0.0", "Unity.TextMeshPro": "1.0.0", "Unity.XR.CoreUtils": "1.0.0", "Unity.XR.Management": "1.0.0", "Unity.XR.OpenXR": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Autohand.MetaXR.Editor.dll": {}}, "runtime": {"bin/placeholder/Autohand.MetaXR.Editor.dll": {}}}, "Autohand.XR/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"AutoHandAssembly": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Autohand.XR.dll": {}}, "runtime": {"bin/placeholder/Autohand.XR.dll": {}}}, "Autohand.XR.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"AutoHandAssembly": "1.0.0", "Autohand.XR": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Autohand.XR.Editor.dll": {}}, "runtime": {"bin/placeholder/Autohand.XR.Editor.dll": {}}}, "AutoHandAssembly/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"NaughtyAttributes.Core": "1.0.0", "NaughtyAttributes.Editor": "1.0.0", "Unity.TextMeshPro": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/AutoHandAssembly.dll": {}}, "runtime": {"bin/placeholder/AutoHandAssembly.dll": {}}}, "Meta.Net.endel.nativewebsocket/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.Net.endel.nativewebsocket.dll": {}}, "runtime": {"bin/placeholder/Meta.Net.endel.nativewebsocket.dll": {}}}, "Meta.Voice.Hub.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.Voice.Hub.Runtime": "1.0.0", "Meta.WitAi.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0", "VoiceSDK.Telemetry": "1.0.0"}, "compile": {"bin/placeholder/Meta.Voice.Hub.Editor.dll": {}}, "runtime": {"bin/placeholder/Meta.Voice.Hub.Editor.dll": {}}}, "Meta.Voice.Hub.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.Voice.Hub.Runtime.dll": {}}, "runtime": {"bin/placeholder/Meta.Voice.Hub.Runtime.dll": {}}}, "Meta.Voice.NLayer/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.Voice.NLayer.dll": {}}, "runtime": {"bin/placeholder/Meta.Voice.NLayer.dll": {}}}, "Meta.Voice.Opus/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.Voice.Opus.dll": {}}, "runtime": {"bin/placeholder/Meta.Voice.Opus.dll": {}}}, "Meta.Voice.VSDKHub.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.Voice.Hub.Editor": "1.0.0", "Meta.Voice.Hub.Runtime": "1.0.0", "Meta.WitAi.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0", "VoiceSDK.Telemetry": "1.0.0"}, "compile": {"bin/placeholder/Meta.Voice.VSDKHub.Editor.dll": {}}, "runtime": {"bin/placeholder/Meta.Voice.VSDKHub.Editor.dll": {}}}, "Meta.VoiceSDK.Mic.Common/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.WitAi.Lib": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.VoiceSDK.Mic.Common.dll": {}}, "runtime": {"bin/placeholder/Meta.VoiceSDK.Mic.Common.dll": {}}}, "Meta.VoiceSDK.Mic.Other/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.VoiceSDK.Mic.Common": "1.0.0", "Meta.WitAi.Lib": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.VoiceSDK.Mic.Other.dll": {}}, "runtime": {"bin/placeholder/Meta.VoiceSDK.Mic.Other.dll": {}}}, "Meta.VoiceSDK.Mic.WebGL/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.VoiceSDK.Mic.Common": "1.0.0", "Meta.WitAi.Lib": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.VoiceSDK.Mic.WebGL.dll": {}}, "runtime": {"bin/placeholder/Meta.VoiceSDK.Mic.WebGL.dll": {}}}, "Meta.Wit.Composer/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.WitAI.Lib.Editor": "1.0.0", "Meta.WitAi": "1.0.0", "Meta.WitAi.Lib": "1.0.0", "Meta.WitAi.TTS": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.Wit.Composer.dll": {}}, "runtime": {"bin/placeholder/Meta.Wit.Composer.dll": {}}}, "Meta.Wit.Composer.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.Wit.Composer": "1.0.0", "Meta.WitAI.Lib.Editor": "1.0.0", "Meta.WitAi": "1.0.0", "Meta.WitAi.Editor": "1.0.0", "Meta.WitAi.Lib": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.Wit.Composer.Editor.dll": {}}, "runtime": {"bin/placeholder/Meta.Wit.Composer.Editor.dll": {}}}, "Meta.Wit.Dictation/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.WitAi": "1.0.0", "Meta.WitAi.Lib": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.Wit.Dictation.dll": {}}, "runtime": {"bin/placeholder/Meta.Wit.Dictation.dll": {}}}, "Meta.Wit.Dictation.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.Wit.Dictation": "1.0.0", "Meta.WitAi.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.Wit.Dictation.Editor.dll": {}}, "runtime": {"bin/placeholder/Meta.Wit.Dictation.Editor.dll": {}}}, "Meta.WitAi/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.Net.endel.nativewebsocket": "1.0.0", "Meta.Voice.NLayer": "1.0.0", "Meta.Voice.Opus": "1.0.0", "Meta.VoiceSDK.Mic.Common": "1.0.0", "Meta.VoiceSDK.Mic.Other": "1.0.0", "Meta.VoiceSDK.Mic.WebGL": "1.0.0", "Meta.WitAi.Lib": "1.0.0", "Unity.InputSystem": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0", "VoiceSDK.Telemetry": "1.0.0", "VoiceSDK.Telemetry.Runtime": "1.0.0"}, "compile": {"bin/placeholder/Meta.WitAi.dll": {}}, "runtime": {"bin/placeholder/Meta.WitAi.dll": {}}}, "Meta.WitAi.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.VoiceSDK.Mic.Common": "1.0.0", "Meta.VoiceSDK.Mic.Other": "1.0.0", "Meta.WitAI.Lib.Editor": "1.0.0", "Meta.WitAi": "1.0.0", "Meta.WitAi.Lib": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0", "VoiceSDK.Telemetry": "1.0.0"}, "compile": {"bin/placeholder/Meta.WitAi.Editor.dll": {}}, "runtime": {"bin/placeholder/Meta.WitAi.Editor.dll": {}}}, "Meta.WitAi.Lib/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.Net.endel.nativewebsocket": "1.0.0", "Meta.Voice.NLayer": "1.0.0", "Meta.Voice.Opus": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0", "VoiceSDK.Telemetry": "1.0.0", "VoiceSDK.Telemetry.Runtime": "1.0.0"}, "compile": {"bin/placeholder/Meta.WitAi.Lib.dll": {}}, "runtime": {"bin/placeholder/Meta.WitAi.Lib.dll": {}}}, "Meta.WitAI.Lib.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.WitAi": "1.0.0", "Meta.WitAi.Lib": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.WitAI.Lib.Editor.dll": {}}, "runtime": {"bin/placeholder/Meta.WitAI.Lib.Editor.dll": {}}}, "Meta.WitAi.TTS/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.WitAi": "1.0.0", "Meta.WitAi.Lib": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.WitAi.TTS.dll": {}}, "runtime": {"bin/placeholder/Meta.WitAi.TTS.dll": {}}}, "Meta.WitAi.TTS.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.WitAI.Lib.Editor": "1.0.0", "Meta.WitAi": "1.0.0", "Meta.WitAi.Editor": "1.0.0", "Meta.WitAi.Lib": "1.0.0", "Meta.WitAi.TTS": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.WitAi.TTS.Editor.dll": {}}, "runtime": {"bin/placeholder/Meta.WitAi.TTS.Editor.dll": {}}}, "Meta.XR.Audio/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.Audio.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.Audio.dll": {}}}, "Meta.XR.Audio.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.Audio": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.Audio.Editor.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.Audio.Editor.dll": {}}}, "Meta.XR.BuildingBlocks/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.ImmersiveDebugger.Interface": "1.0.0", "Oculus.VR": "1.0.0", "Unity.InputSystem": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.BuildingBlocks.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.BuildingBlocks.dll": {}}}, "Meta.XR.BuildingBlocks.DepthAPI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.BuildingBlocks": "1.0.0", "Oculus.VR": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "Unity.XR.Oculus": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.BuildingBlocks.DepthAPI.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.BuildingBlocks.DepthAPI.dll": {}}}, "Meta.XR.BuildingBlocks.DepthAPI.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.BuildingBlocks": "1.0.0", "Meta.XR.BuildingBlocks.DepthAPI": "1.0.0", "Meta.XR.BuildingBlocks.Editor": "1.0.0", "Meta.XR.Editor.Tags": "1.0.0", "Meta.XR.Editor.UserInterface": "1.0.0", "Meta.XR.EnvironmentDepth": "1.0.0", "Meta.XR.Guides.Editor": "1.0.0", "Oculus.VR": "1.0.0", "Oculus.VR.Editor": "1.0.0", "Unity.XR.Oculus": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.BuildingBlocks.DepthAPI.Editor.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.BuildingBlocks.DepthAPI.Editor.dll": {}}}, "Meta.XR.BuildingBlocks.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.Wit.Dictation": "1.0.0", "Meta.WitAi": "1.0.0", "Meta.WitAi.Editor": "1.0.0", "Meta.WitAi.TTS": "1.0.0", "Meta.WitAi.TTS.Editor": "1.0.0", "Meta.XR.Audio": "1.0.0", "Meta.XR.BuildingBlocks": "1.0.0", "Meta.XR.Editor.Callbacks": "1.0.0", "Meta.XR.Editor.Id": "1.0.0", "Meta.XR.Editor.Reflection": "1.0.0", "Meta.XR.Editor.Settings": "1.0.0", "Meta.XR.Editor.Tags": "1.0.0", "Meta.XR.Editor.ToolingSupport": "1.0.0", "Meta.XR.Editor.UPST.Notifications": "1.0.0", "Meta.XR.Editor.UserInterface": "1.0.0", "Meta.XR.Guides.Editor": "1.0.0", "Oculus.Haptics": "1.0.0", "Oculus.Platform": "1.0.0", "Oculus.VR": "1.0.0", "Oculus.VR.Editor": "1.0.0", "Oculus.VR.Scripts.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0", "VoiceSDK.Dictation.Editor": "1.0.0", "VoiceSDK.Dictation.Runtime": "1.0.0", "VoiceSDK.Editor": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.BuildingBlocks.Editor.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.BuildingBlocks.Editor.dll": {}}}, "Meta.XR.Editor.Callbacks/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.Editor.Callbacks.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.Editor.Callbacks.dll": {}}}, "Meta.XR.Editor.Guide.About/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.BuildingBlocks.Editor": "1.0.0", "Meta.XR.Editor.Id": "1.0.0", "Meta.XR.Editor.Settings": "1.0.0", "Meta.XR.Editor.StatusMenu": "1.0.0", "Meta.XR.Editor.ToolingSupport": "1.0.0", "Meta.XR.Editor.UserInterface": "1.0.0", "Meta.XR.Guides.Editor": "1.0.0", "Oculus.VR": "1.0.0", "Oculus.VR.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.Editor.Guide.About.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.Editor.Guide.About.dll": {}}}, "Meta.XR.Editor.Id/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.Editor.Id.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.Editor.Id.dll": {}}}, "Meta.XR.Editor.Notifications/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.Editor.Id": "1.0.0", "Meta.XR.Editor.Reflection": "1.0.0", "Meta.XR.Editor.Settings": "1.0.0", "Meta.XR.Editor.ToolingSupport": "1.0.0", "Meta.XR.Editor.UserInterface": "1.0.0", "Meta.XR.Guides.Editor": "1.0.0", "Oculus.VR": "1.0.0", "Oculus.VR.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.Editor.Notifications.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.Editor.Notifications.dll": {}}}, "Meta.XR.Editor.PlayCompanion/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.Editor.Id": "1.0.0", "Meta.XR.Editor.Reflection": "1.0.0", "Meta.XR.Editor.Settings": "1.0.0", "Meta.XR.Editor.ToolingSupport": "1.0.0", "Meta.XR.Editor.UserInterface": "1.0.0", "Oculus.VR.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.Editor.PlayCompanion.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.Editor.PlayCompanion.dll": {}}}, "Meta.XR.Editor.Reflection/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.Editor.Reflection.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.Editor.Reflection.dll": {}}}, "Meta.XR.Editor.Settings/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.Editor.Id": "1.0.0", "Meta.XR.Editor.UserInterface": "1.0.0", "Oculus.VR": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.Editor.Settings.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.Editor.Settings.dll": {}}}, "Meta.XR.Editor.StatusMenu/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.Editor.Id": "1.0.0", "Meta.XR.Editor.Reflection": "1.0.0", "Meta.XR.Editor.Settings": "1.0.0", "Meta.XR.Editor.Tags": "1.0.0", "Meta.XR.Editor.ToolingSupport": "1.0.0", "Meta.XR.Editor.UserInterface": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.Editor.StatusMenu.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.Editor.StatusMenu.dll": {}}}, "Meta.XR.Editor.Tags/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.Editor.Id": "1.0.0", "Meta.XR.Editor.Settings": "1.0.0", "Meta.XR.Editor.UserInterface": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.Editor.Tags.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.Editor.Tags.dll": {}}}, "Meta.XR.Editor.TelemetryUI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.Editor.Id": "1.0.0", "Meta.XR.Editor.Notifications": "1.0.0", "Meta.XR.Editor.Settings": "1.0.0", "Meta.XR.Editor.ToolingSupport": "1.0.0", "Meta.XR.Editor.UserInterface": "1.0.0", "Meta.XR.Guides.Editor": "1.0.0", "Oculus.VR": "1.0.0", "Oculus.VR.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.Editor.TelemetryUI.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.Editor.TelemetryUI.dll": {}}}, "Meta.XR.Editor.ToolingSupport/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.Editor.Id": "1.0.0", "Meta.XR.Editor.Reflection": "1.0.0", "Meta.XR.Editor.Settings": "1.0.0", "Meta.XR.Editor.Tags": "1.0.0", "Meta.XR.Editor.UserInterface": "1.0.0", "Oculus.VR": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.Editor.ToolingSupport.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.Editor.ToolingSupport.dll": {}}}, "Meta.XR.Editor.UPST.Notifications/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.Editor.Id": "1.0.0", "Meta.XR.Editor.Notifications": "1.0.0", "Meta.XR.Editor.Settings": "1.0.0", "Meta.XR.Editor.UserInterface": "1.0.0", "Oculus.VR.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.Editor.UPST.Notifications.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.Editor.UPST.Notifications.dll": {}}}, "Meta.XR.Editor.UserInterface/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.Editor.Id": "1.0.0", "Oculus.VR": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.Editor.UserInterface.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.Editor.UserInterface.dll": {}}}, "Meta.XR.EnvironmentDepth/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Oculus.VR": "1.0.0", "Unity.XR.Management": "1.0.0", "Unity.XR.Oculus": "1.0.0", "Unity.XR.OpenXR": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.EnvironmentDepth.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.EnvironmentDepth.dll": {}}}, "Meta.XR.EnvironmentDepth.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.EnvironmentDepth": "1.0.0", "Oculus.VR": "1.0.0", "Oculus.VR.Editor": "1.0.0", "Unity.XR.Oculus": "1.0.0", "Unity.XR.OpenXR": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.EnvironmentDepth.Editor.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.EnvironmentDepth.Editor.dll": {}}}, "Meta.XR.Guides.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.Editor.Id": "1.0.0", "Meta.XR.Editor.Settings": "1.0.0", "Meta.XR.Editor.ToolingSupport": "1.0.0", "Meta.XR.Editor.UserInterface": "1.0.0", "Oculus.Platform": "1.0.0", "Oculus.VR": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.Guides.Editor.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.Guides.Editor.dll": {}}}, "Meta.XR.ImmersiveDebugger/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.Editor.Callbacks": "1.0.0", "Meta.XR.ImmersiveDebugger.Interface": "1.0.0", "Oculus.VR": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.ImmersiveDebugger.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.ImmersiveDebugger.dll": {}}}, "Meta.XR.ImmersiveDebugger.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.Editor.Callbacks": "1.0.0", "Meta.XR.Editor.Id": "1.0.0", "Meta.XR.Editor.Settings": "1.0.0", "Meta.XR.Editor.ToolingSupport": "1.0.0", "Meta.XR.Editor.UserInterface": "1.0.0", "Meta.XR.ImmersiveDebugger": "1.0.0", "Meta.XR.ImmersiveDebugger.Interface": "1.0.0", "Oculus.VR": "1.0.0", "Oculus.VR.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.ImmersiveDebugger.Editor.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.ImmersiveDebugger.Editor.dll": {}}}, "Meta.XR.ImmersiveDebugger.Interface/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.ImmersiveDebugger.Interface.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.ImmersiveDebugger.Interface.dll": {}}}, "meta.xr.mrutilitykit/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.EnvironmentDepth": "1.0.0", "Meta.XR.ImmersiveDebugger": "1.0.0", "Oculus.Interaction": "1.0.0", "Oculus.VR": "1.0.0", "Oculus.VR.Editor": "1.0.0", "Unity.AI.Navigation": "1.0.0", "Unity.TextMeshPro": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/meta.xr.mrutilitykit.dll": {}}, "runtime": {"bin/placeholder/meta.xr.mrutilitykit.dll": {}}}, "meta.xr.mrutilitykit.editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.BuildingBlocks": "1.0.0", "Meta.XR.BuildingBlocks.Editor": "1.0.0", "Meta.XR.EnvironmentDepth.Editor": "1.0.0", "Meta.XR.ImmersiveDebugger": "1.0.0", "Oculus.Interaction": "1.0.0", "Oculus.Interaction.OVR.Editor": "1.0.0", "Oculus.VR": "1.0.0", "Oculus.VR.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0", "meta.xr.mrutilitykit": "1.0.0"}, "compile": {"bin/placeholder/meta.xr.mrutilitykit.editor.dll": {}}, "runtime": {"bin/placeholder/meta.xr.mrutilitykit.editor.dll": {}}}, "Meta.XR.MultiplayerBlocks.Fusion.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.BuildingBlocks": "1.0.0", "Meta.XR.BuildingBlocks.Editor": "1.0.0", "Meta.XR.MultiplayerBlocks.Shared": "1.0.0", "Meta.XR.MultiplayerBlocks.Shared.Editor": "1.0.0", "Oculus.Interaction": "1.0.0", "Oculus.Interaction.OVR.Editor": "1.0.0", "Oculus.VR": "1.0.0", "Oculus.VR.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.MultiplayerBlocks.Fusion.Editor.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.MultiplayerBlocks.Fusion.Editor.dll": {}}}, "Meta.XR.MultiplayerBlocks.NGO.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.BuildingBlocks": "1.0.0", "Meta.XR.BuildingBlocks.Editor": "1.0.0", "Meta.XR.MultiplayerBlocks.Shared": "1.0.0", "Meta.XR.MultiplayerBlocks.Shared.Editor": "1.0.0", "Oculus.Interaction": "1.0.0", "Oculus.Interaction.Editor": "1.0.0", "Oculus.Interaction.OVR": "1.0.0", "Oculus.Interaction.OVR.Editor": "1.0.0", "Oculus.VR": "1.0.0", "Oculus.VR.Editor": "1.0.0", "Oculus.VR.Scripts.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.MultiplayerBlocks.NGO.Editor.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.MultiplayerBlocks.NGO.Editor.dll": {}}}, "Meta.XR.MultiplayerBlocks.Shared/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.BuildingBlocks": "1.0.0", "Meta.XR.ImmersiveDebugger.Interface": "1.0.0", "Oculus.Interaction": "1.0.0", "Oculus.Interaction.OVR": "1.0.0", "Oculus.Platform": "1.0.0", "Oculus.VR": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0", "meta.xr.mrutilitykit": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.MultiplayerBlocks.Shared.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.MultiplayerBlocks.Shared.dll": {}}}, "Meta.XR.MultiplayerBlocks.Shared.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.BuildingBlocks": "1.0.0", "Meta.XR.BuildingBlocks.Editor": "1.0.0", "Meta.XR.MultiplayerBlocks.Shared": "1.0.0", "Oculus.Interaction": "1.0.0", "Oculus.Interaction.OVR.Editor": "1.0.0", "Oculus.VR": "1.0.0", "Oculus.VR.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0", "meta.xr.mrutilitykit": "1.0.0"}, "compile": {"bin/placeholder/Meta.XR.MultiplayerBlocks.Shared.Editor.dll": {}}, "runtime": {"bin/placeholder/Meta.XR.MultiplayerBlocks.Shared.Editor.dll": {}}}, "MetaXrSimulator.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.Editor.Id": "1.0.0", "Meta.XR.Editor.PlayCompanion": "1.0.0", "Meta.XR.Editor.Settings": "1.0.0", "Meta.XR.Editor.StatusMenu": "1.0.0", "Meta.XR.Editor.ToolingSupport": "1.0.0", "Meta.XR.Editor.UserInterface": "1.0.0", "Oculus.VR": "1.0.0", "Oculus.VR.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/MetaXrSimulator.Editor.dll": {}}, "runtime": {"bin/placeholder/MetaXrSimulator.Editor.dll": {}}}, "NaughtyAttributes.Core/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/NaughtyAttributes.Core.dll": {}}, "runtime": {"bin/placeholder/NaughtyAttributes.Core.dll": {}}}, "NaughtyAttributes.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"NaughtyAttributes.Core": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/NaughtyAttributes.Editor.dll": {}}, "runtime": {"bin/placeholder/NaughtyAttributes.Editor.dll": {}}}, "NewAssembly/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Oculus.Interaction.Editor": "1.0.0", "Oculus.Interaction.Samples": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/NewAssembly.dll": {}}, "runtime": {"bin/placeholder/NewAssembly.dll": {}}}, "Oculus.Haptics/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Oculus.VR": "1.0.0", "Unity.XR.OpenXR": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Oculus.Haptics.dll": {}}, "runtime": {"bin/placeholder/Oculus.Haptics.dll": {}}}, "Oculus.Haptics.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Oculus.Haptics": "1.0.0", "Oculus.VR": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Oculus.Haptics.Editor.dll": {}}, "runtime": {"bin/placeholder/Oculus.Haptics.Editor.dll": {}}}, "Oculus.Interaction/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.TextMeshPro": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Oculus.Interaction.dll": {}}, "runtime": {"bin/placeholder/Oculus.Interaction.dll": {}}}, "Oculus.Interaction.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Oculus.Interaction": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Oculus.Interaction.Editor.dll": {}}, "runtime": {"bin/placeholder/Oculus.Interaction.Editor.dll": {}}}, "Oculus.Interaction.Editor.UnityXR/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Oculus.Interaction": "1.0.0", "Oculus.Interaction.Editor": "1.0.0", "Oculus.Interaction.UnityXR": "1.0.0", "Unity.InputSystem": "1.0.0", "Unity.XR.CoreUtils": "1.0.0", "Unity.XR.Management": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Oculus.Interaction.Editor.UnityXR.dll": {}}, "runtime": {"bin/placeholder/Oculus.Interaction.Editor.UnityXR.dll": {}}}, "Oculus.Interaction.InterfaceSupport/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Oculus.Interaction": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Oculus.Interaction.InterfaceSupport.dll": {}}, "runtime": {"bin/placeholder/Oculus.Interaction.InterfaceSupport.dll": {}}}, "Oculus.Interaction.OVR/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Oculus.Interaction": "1.0.0", "Oculus.VR": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Oculus.Interaction.OVR.dll": {}}, "runtime": {"bin/placeholder/Oculus.Interaction.OVR.dll": {}}}, "Oculus.Interaction.OVR.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.BuildingBlocks": "1.0.0", "Meta.XR.BuildingBlocks.Editor": "1.0.0", "Oculus.Interaction": "1.0.0", "Oculus.Interaction.Editor": "1.0.0", "Oculus.Interaction.OVR": "1.0.0", "Oculus.VR": "1.0.0", "Oculus.VR.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Oculus.Interaction.OVR.Editor.dll": {}}, "runtime": {"bin/placeholder/Oculus.Interaction.OVR.Editor.dll": {}}}, "Oculus.Interaction.OVR.Samples/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Oculus.Interaction": "1.0.0", "Oculus.Interaction.Samples": "1.0.0", "Oculus.VR": "1.0.0", "Unity.TextMeshPro": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Oculus.Interaction.OVR.Samples.dll": {}}, "runtime": {"bin/placeholder/Oculus.Interaction.OVR.Samples.dll": {}}}, "Oculus.Interaction.Samples/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Oculus.Interaction": "1.0.0", "Unity.TextMeshPro": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Oculus.Interaction.Samples.dll": {}}, "runtime": {"bin/placeholder/Oculus.Interaction.Samples.dll": {}}}, "Oculus.Interaction.Samples.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Oculus.Interaction": "1.0.0", "Oculus.Interaction.Editor": "1.0.0", "Oculus.Interaction.Samples": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Oculus.Interaction.Samples.Editor.dll": {}}, "runtime": {"bin/placeholder/Oculus.Interaction.Samples.Editor.dll": {}}}, "Oculus.Interaction.UnityXR/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Oculus.Interaction": "1.0.0", "Unity.InputSystem": "1.0.0", "Unity.XR.CoreUtils": "1.0.0", "Unity.XR.Management": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Oculus.Interaction.UnityXR.dll": {}}, "runtime": {"bin/placeholder/Oculus.Interaction.UnityXR.dll": {}}}, "Oculus.Platform/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Oculus.Platform.dll": {}}, "runtime": {"bin/placeholder/Oculus.Platform.dll": {}}}, "Oculus.Platform.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Oculus.Platform": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Oculus.Platform.Editor.dll": {}}, "runtime": {"bin/placeholder/Oculus.Platform.Editor.dll": {}}}, "Oculus.VR/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.Editor.Callbacks": "1.0.0", "Unity.InputSystem": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "Unity.TextMeshPro": "1.0.0", "Unity.XR.Management": "1.0.0", "Unity.XR.Oculus": "1.0.0", "Unity.XR.OpenXR": "1.0.0", "Unity.XR.OpenXR.Editor": "1.0.0", "Unity.XR.OpenXR.Features.MetaQuestSupport": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Oculus.VR.dll": {}}, "runtime": {"bin/placeholder/Oculus.VR.dll": {}}}, "Oculus.VR.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.XR.BuildingBlocks": "1.0.0", "Meta.XR.Editor.Callbacks": "1.0.0", "Meta.XR.Editor.Id": "1.0.0", "Meta.XR.Editor.Reflection": "1.0.0", "Meta.XR.Editor.Settings": "1.0.0", "Meta.XR.Editor.StatusMenu": "1.0.0", "Meta.XR.Editor.ToolingSupport": "1.0.0", "Meta.XR.Editor.UserInterface": "1.0.0", "Meta.XR.Guides.Editor": "1.0.0", "Oculus.VR": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "Unity.TextMeshPro": "1.0.0", "Unity.TextMeshPro.Editor": "1.0.0", "Unity.XR.CoreUtils.Editor": "1.0.0", "Unity.XR.Management": "1.0.0", "Unity.XR.Management.Editor": "1.0.0", "Unity.XR.Oculus": "1.0.0", "Unity.XR.OpenXR": "1.0.0", "Unity.XR.OpenXR.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Oculus.VR.Editor.dll": {}}, "runtime": {"bin/placeholder/Oculus.VR.Editor.dll": {}}}, "Oculus.VR.Scripts.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Oculus.VR": "1.0.0", "Oculus.VR.Editor": "1.0.0", "Unity.TextMeshPro": "1.0.0", "Unity.XR.Management": "1.0.0", "Unity.XR.Management.Editor": "1.0.0", "Unity.XR.Oculus": "1.0.0", "Unity.XR.OpenXR": "1.0.0", "Unity.XR.OpenXR.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Oculus.VR.Scripts.Editor.dll": {}}, "runtime": {"bin/placeholder/Oculus.VR.Scripts.Editor.dll": {}}}, "PPv2URPConverters/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Editor": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Editor": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/PPv2URPConverters.dll": {}}, "runtime": {"bin/placeholder/PPv2URPConverters.dll": {}}}, "RootMotion/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/RootMotion.dll": {}}, "runtime": {"bin/placeholder/RootMotion.dll": {}}}, "RootMotionEditor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"RootMotion": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/RootMotionEditor.dll": {}}, "runtime": {"bin/placeholder/RootMotionEditor.dll": {}}}, "Unity.AI.Navigation/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AI.Navigation.dll": {}}, "runtime": {"bin/placeholder/Unity.AI.Navigation.dll": {}}}, "Unity.AI.Navigation.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AI.Navigation": "1.0.0", "Unity.AI.Navigation.Editor.ConversionSystem": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AI.Navigation.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.AI.Navigation.Editor.dll": {}}}, "Unity.AI.Navigation.Editor.ConversionSystem/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AI.Navigation.Editor.ConversionSystem.dll": {}}, "runtime": {"bin/placeholder/Unity.AI.Navigation.Editor.ConversionSystem.dll": {}}}, "Unity.AI.Navigation.Updater/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AI.Navigation": "1.0.0", "Unity.AI.Navigation.Editor": "1.0.0", "Unity.AI.Navigation.Editor.ConversionSystem": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AI.Navigation.Updater.dll": {}}, "runtime": {"bin/placeholder/Unity.AI.Navigation.Updater.dll": {}}}, "Unity.Burst/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Burst.dll": {}}, "runtime": {"bin/placeholder/Unity.Burst.dll": {}}}, "Unity.Burst.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Burst.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Burst.Editor.dll": {}}}, "Unity.Collections/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Collections.dll": {}}, "runtime": {"bin/placeholder/Unity.Collections.dll": {}}}, "Unity.Collections.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Collections": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Collections.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Collections.Editor.dll": {}}}, "Unity.InputSystem/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.InputSystem.dll": {}}, "runtime": {"bin/placeholder/Unity.InputSystem.dll": {}}}, "Unity.InputSystem.ForUI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.InputSystem.ForUI.dll": {}}, "runtime": {"bin/placeholder/Unity.InputSystem.ForUI.dll": {}}}, "Unity.Mathematics/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Mathematics.dll": {}}, "runtime": {"bin/placeholder/Unity.Mathematics.dll": {}}}, "Unity.Mathematics.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Mathematics": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Mathematics.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Mathematics.Editor.dll": {}}}, "Unity.Multiplayer.Center.Common/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Multiplayer.Center.Common.dll": {}}, "runtime": {"bin/placeholder/Unity.Multiplayer.Center.Common.dll": {}}}, "Unity.Multiplayer.Center.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Multiplayer.Center.Common": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Multiplayer.Center.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Multiplayer.Center.Editor.dll": {}}}, "Unity.PlasticSCM.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.PlasticSCM.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.PlasticSCM.Editor.dll": {}}}, "Unity.Rendering.LightTransport.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Rendering.LightTransport.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Rendering.LightTransport.Editor.dll": {}}}, "Unity.Rendering.LightTransport.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Rendering.LightTransport.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.Rendering.LightTransport.Runtime.dll": {}}}, "Unity.RenderPipeline.Universal.ShaderLibrary/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipeline.Universal.ShaderLibrary.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipeline.Universal.ShaderLibrary.dll": {}}}, "Unity.RenderPipelines.Core.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime": "1.0.0", "Unity.Rendering.LightTransport.Runtime": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Editor.dll": {}}}, "Unity.RenderPipelines.Core.Editor.Shared/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Editor.Shared.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Editor.Shared.dll": {}}}, "Unity.RenderPipelines.Core.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.InputSystem": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.dll": {}}}, "Unity.RenderPipelines.Core.Runtime.Shared/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.Shared.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.Shared.dll": {}}}, "Unity.RenderPipelines.Core.ShaderLibrary/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.ShaderLibrary.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.ShaderLibrary.dll": {}}}, "Unity.RenderPipelines.GPUDriven.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.GPUDriven.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.GPUDriven.Runtime.dll": {}}}, "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll": {}}}, "Unity.RenderPipelines.Universal.2D.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipeline.Universal.ShaderLibrary": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.2D.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.2D.Runtime.dll": {}}}, "Unity.RenderPipelines.Universal.Config.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.Config.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.Config.Runtime.dll": {}}}, "Unity.RenderPipelines.Universal.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst.Editor": "1.0.0", "Unity.Mathematics.Editor": "1.0.0", "Unity.RenderPipelines.Core.Editor": "1.0.0", "Unity.RenderPipelines.Core.Editor.Shared": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Core.Runtime.Shared": "1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.2D.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "Unity.ShaderGraph.Editor": "1.0.0", "Unity.XR.Management": "1.0.0", "Unity.XR.Management.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.Editor.dll": {}}}, "Unity.RenderPipelines.Universal.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipeline.Universal.ShaderLibrary": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Core.Runtime.Shared": "1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Config.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.Runtime.dll": {}}}, "Unity.RenderPipelines.Universal.Shaders/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.Shaders.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.Shaders.dll": {}}}, "Unity.Rider.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Rider.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Rider.Editor.dll": {}}}, "Unity.Searcher.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Searcher.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Searcher.Editor.dll": {}}}, "Unity.ShaderGraph.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Editor": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.Searcher.Editor": "1.0.0", "Unity.ShaderGraph.Utilities": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.ShaderGraph.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.ShaderGraph.Editor.dll": {}}}, "Unity.ShaderGraph.Utilities/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.ShaderGraph.Utilities.dll": {}}, "runtime": {"bin/placeholder/Unity.ShaderGraph.Utilities.dll": {}}}, "Unity.TextMeshPro/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.TextMeshPro.dll": {}}, "runtime": {"bin/placeholder/Unity.TextMeshPro.dll": {}}}, "Unity.TextMeshPro.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.TextMeshPro": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.TextMeshPro.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.TextMeshPro.Editor.dll": {}}}, "Unity.Timeline/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Timeline.dll": {}}, "runtime": {"bin/placeholder/Unity.Timeline.dll": {}}}, "Unity.Timeline.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Timeline": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Timeline.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Timeline.Editor.dll": {}}}, "Unity.VisualScripting.Core/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Core.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Core.dll": {}}}, "Unity.VisualScripting.Core.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Core.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Core.Editor.dll": {}}}, "Unity.VisualScripting.Flow/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.VisualScripting.Core": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Flow.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Flow.dll": {}}}, "Unity.VisualScripting.Flow.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Core.Editor": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Flow.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Flow.Editor.dll": {}}}, "Unity.VisualScripting.SettingsProvider.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Core.Editor": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "Unity.VisualScripting.Flow.Editor": "1.0.0", "Unity.VisualScripting.State": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.SettingsProvider.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.SettingsProvider.Editor.dll": {}}}, "Unity.VisualScripting.Shared.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Core.Editor": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "Unity.VisualScripting.Flow.Editor": "1.0.0", "Unity.VisualScripting.State": "1.0.0", "Unity.VisualScripting.State.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Shared.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Shared.Editor.dll": {}}}, "Unity.VisualScripting.State/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.State.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.State.dll": {}}}, "Unity.VisualScripting.State.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Core.Editor": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "Unity.VisualScripting.Flow.Editor": "1.0.0", "Unity.VisualScripting.State": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.State.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.State.Editor.dll": {}}}, "Unity.VisualStudio.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualStudio.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualStudio.Editor.dll": {}}}, "Unity.XR.CoreUtils/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.SpatialTracking": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.CoreUtils.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.CoreUtils.dll": {}}}, "Unity.XR.CoreUtils.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.XR.CoreUtils": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.CoreUtils.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.CoreUtils.Editor.dll": {}}}, "Unity.XR.Management/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.Management.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Management.dll": {}}}, "Unity.XR.Management.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.XR.CoreUtils.Editor": "1.0.0", "Unity.XR.Management": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.Management.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Management.Editor.dll": {}}}, "Unity.XR.Oculus/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.XR.Management": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.Oculus.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Oculus.dll": {}}}, "Unity.XR.Oculus.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.CoreUtils.Editor": "1.0.0", "Unity.XR.Management": "1.0.0", "Unity.XR.Management.Editor": "1.0.0", "Unity.XR.Oculus": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.Oculus.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Oculus.Editor.dll": {}}}, "Unity.XR.OpenXR/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "Unity.XR.Management": "1.0.0", "Unity.XR.Management.Editor": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.OpenXR.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.OpenXR.dll": {}}}, "Unity.XR.OpenXR.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.XR.CoreUtils.Editor": "1.0.0", "Unity.XR.Management": "1.0.0", "Unity.XR.Management.Editor": "1.0.0", "Unity.XR.OpenXR": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.OpenXR.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.OpenXR.Editor.dll": {}}}, "Unity.XR.OpenXR.Features.ConformanceAutomation/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.OpenXR": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.OpenXR.Features.ConformanceAutomation.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.OpenXR.Features.ConformanceAutomation.dll": {}}}, "Unity.XR.OpenXR.Features.MetaQuestSupport/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.OpenXR": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.OpenXR.Features.MetaQuestSupport.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.OpenXR.Features.MetaQuestSupport.dll": {}}}, "Unity.XR.OpenXR.Features.MetaQuestSupport.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.Management.Editor": "1.0.0", "Unity.XR.OpenXR": "1.0.0", "Unity.XR.OpenXR.Editor": "1.0.0", "Unity.XR.OpenXR.Features.MetaQuestSupport": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.OpenXR.Features.MetaQuestSupport.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.OpenXR.Features.MetaQuestSupport.Editor.dll": {}}}, "Unity.XR.OpenXR.Features.OculusQuestSupport/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.OpenXR": "1.0.0", "Unity.XR.OpenXR.Features.MetaQuestSupport": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.OpenXR.Features.OculusQuestSupport.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.OpenXR.Features.OculusQuestSupport.dll": {}}}, "Unity.XR.OpenXR.Features.OculusQuestSupport.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.OpenXR": "1.0.0", "Unity.XR.OpenXR.Editor": "1.0.0", "Unity.XR.OpenXR.Features.OculusQuestSupport": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.OpenXR.Features.OculusQuestSupport.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.OpenXR.Features.OculusQuestSupport.Editor.dll": {}}}, "Unity.XR.OpenXR.Features.RuntimeDebugger/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.OpenXR": "1.0.0", "Unity.XR.OpenXR.Editor": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.OpenXR.Features.RuntimeDebugger.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.OpenXR.Features.RuntimeDebugger.dll": {}}}, "Unity.XR.OpenXR.Features.RuntimeDebugger.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.OpenXR": "1.0.0", "Unity.XR.OpenXR.Editor": "1.0.0", "Unity.XR.OpenXR.Features.RuntimeDebugger": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.OpenXR.Features.RuntimeDebugger.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.OpenXR.Features.RuntimeDebugger.Editor.dll": {}}}, "UnityEditor.SpatialTracking/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.SpatialTracking": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.SpatialTracking.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.SpatialTracking.dll": {}}}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEngine.TestRunner": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}}, "UnityEditor.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.UI.dll": {}}}, "UnityEditor.XR.LegacyInputHelpers/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.SpatialTracking": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0", "UnityEngine.XR.LegacyInputHelpers": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.XR.LegacyInputHelpers.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.XR.LegacyInputHelpers.dll": {}}}, "UnityEngine.SpatialTracking/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEngine.SpatialTracking.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.SpatialTracking.dll": {}}}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}}, "UnityEngine.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.UI.dll": {}}}, "UnityEngine.XR.LegacyInputHelpers/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.SpatialTracking": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEngine.XR.LegacyInputHelpers.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.XR.LegacyInputHelpers.dll": {}}}, "VoiceSDK.Dictation.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.Wit.Dictation": "1.0.0", "Meta.WitAi": "1.0.0", "Meta.WitAi.Editor": "1.0.0", "Meta.WitAi.Lib": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0", "VoiceSDK.Dictation.Runtime": "1.0.0"}, "compile": {"bin/placeholder/VoiceSDK.Dictation.Editor.dll": {}}, "runtime": {"bin/placeholder/VoiceSDK.Dictation.Editor.dll": {}}}, "VoiceSDK.Dictation.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"AssistantCoreSDKRuntime": "1.0.0", "Meta.Wit.Dictation": "1.0.0", "Meta.WitAi": "1.0.0", "Meta.WitAi.Lib": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0", "VoiceSDK.Runtime": "1.0.0"}, "compile": {"bin/placeholder/VoiceSDK.Dictation.Runtime.dll": {}}, "runtime": {"bin/placeholder/VoiceSDK.Dictation.Runtime.dll": {}}}, "VoiceSDK.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Meta.Voice.Hub.Editor": "1.0.0", "Meta.Voice.Hub.Runtime": "1.0.0", "Meta.Voice.VSDKHub.Editor": "1.0.0", "Meta.WitAi": "1.0.0", "Meta.WitAi.Editor": "1.0.0", "Meta.WitAi.Lib": "1.0.0", "Meta.WitAi.TTS": "1.0.0", "Meta.WitAi.TTS.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0", "VoiceSDK.Runtime": "1.0.0", "VoiceSDK.Telemetry": "1.0.0"}, "compile": {"bin/placeholder/VoiceSDK.Editor.dll": {}}, "runtime": {"bin/placeholder/VoiceSDK.Editor.dll": {}}}, "VoiceSDK.Editor.Composer/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"AssistantCoreSDKRuntime": "1.0.0", "Meta.Wit.Composer": "1.0.0", "Meta.WitAi": "1.0.0", "Meta.WitAi.Editor": "1.0.0", "Meta.WitAi.TTS": "1.0.0", "Meta.WitAi.TTS.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0", "VoiceSDK.Runtime": "1.0.0", "VoiceSDK.Runtime.Composer": "1.0.0"}, "compile": {"bin/placeholder/VoiceSDK.Editor.Composer.dll": {}}, "runtime": {"bin/placeholder/VoiceSDK.Editor.Composer.dll": {}}}, "VoiceSDK.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"AssistantCoreSDKRuntime": "1.0.0", "AssistantVoiceCommandCommon": "1.0.0", "Meta.WitAi": "1.0.0", "Meta.WitAi.Lib": "1.0.0", "Meta.WitAi.TTS": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/VoiceSDK.Runtime.dll": {}}, "runtime": {"bin/placeholder/VoiceSDK.Runtime.dll": {}}}, "VoiceSDK.Runtime.Composer/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"AssistantCoreSDKRuntime": "1.0.0", "AssistantVoiceCommandCommon": "1.0.0", "Meta.Wit.Composer": "1.0.0", "Meta.WitAi": "1.0.0", "Meta.WitAi.Lib": "1.0.0", "Meta.WitAi.TTS": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0", "VoiceSDK.Runtime": "1.0.0"}, "compile": {"bin/placeholder/VoiceSDK.Runtime.Composer.dll": {}}, "runtime": {"bin/placeholder/VoiceSDK.Runtime.Composer.dll": {}}}, "VoiceSDK.Telemetry/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"AssistantCoreSDKEditor": "1.0.0", "AssistantCoreSDKRuntime": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0", "VoiceSDK.Telemetry.Runtime": "1.0.0"}, "compile": {"bin/placeholder/VoiceSDK.Telemetry.dll": {}}, "runtime": {"bin/placeholder/VoiceSDK.Telemetry.dll": {}}}, "VoiceSDK.Telemetry.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/VoiceSDK.Telemetry.Runtime.dll": {}}, "runtime": {"bin/placeholder/VoiceSDK.Telemetry.Runtime.dll": {}}}}}, "libraries": {"Assembly-CSharp-firstpass/1.0.0": {"type": "project", "path": "Assembly-CSharp-firstpass.csproj", "msbuildProject": "Assembly-CSharp-firstpass.csproj"}, "AssistantCoreSDKEditor/1.0.0": {"type": "project", "path": "AssistantCoreSDKEditor.csproj", "msbuildProject": "AssistantCoreSDKEditor.csproj"}, "AssistantCoreSDKRuntime/1.0.0": {"type": "project", "path": "AssistantCoreSDKRuntime.csproj", "msbuildProject": "AssistantCoreSDKRuntime.csproj"}, "AssistantVoiceCommandCommon/1.0.0": {"type": "project", "path": "AssistantVoiceCommandCommon.csproj", "msbuildProject": "AssistantVoiceCommandCommon.csproj"}, "AssistantVoiceCommandCommon.Editor/1.0.0": {"type": "project", "path": "AssistantV<PERSON>ceCommandCommon.Editor.csproj", "msbuildProject": "AssistantV<PERSON>ceCommandCommon.Editor.csproj"}, "Autohand.Editor/1.0.0": {"type": "project", "path": "Autohand.Editor.csproj", "msbuildProject": "Autohand.Editor.csproj"}, "Autohand.MetaXR/1.0.0": {"type": "project", "path": "Autohand.MetaXR.csproj", "msbuildProject": "Autohand.MetaXR.csproj"}, "Autohand.MetaXR.Editor/1.0.0": {"type": "project", "path": "Autohand.MetaXR.Editor.csproj", "msbuildProject": "Autohand.MetaXR.Editor.csproj"}, "Autohand.XR/1.0.0": {"type": "project", "path": "Autohand.XR.csproj", "msbuildProject": "Autohand.XR.csproj"}, "Autohand.XR.Editor/1.0.0": {"type": "project", "path": "Autohand.XR.Editor.csproj", "msbuildProject": "Autohand.XR.Editor.csproj"}, "AutoHandAssembly/1.0.0": {"type": "project", "path": "AutoHandAssembly.csproj", "msbuildProject": "AutoHandAssembly.csproj"}, "Meta.Net.endel.nativewebsocket/1.0.0": {"type": "project", "path": "Meta.Net.endel.nativewebsocket.csproj", "msbuildProject": "Meta.Net.endel.nativewebsocket.csproj"}, "Meta.Voice.Hub.Editor/1.0.0": {"type": "project", "path": "Meta.Voice.Hub.Editor.csproj", "msbuildProject": "Meta.Voice.Hub.Editor.csproj"}, "Meta.Voice.Hub.Runtime/1.0.0": {"type": "project", "path": "Meta.Voice.Hub.Runtime.csproj", "msbuildProject": "Meta.Voice.Hub.Runtime.csproj"}, "Meta.Voice.NLayer/1.0.0": {"type": "project", "path": "Meta.Voice.NLayer.csproj", "msbuildProject": "Meta.Voice.NLayer.csproj"}, "Meta.Voice.Opus/1.0.0": {"type": "project", "path": "Meta.Voice.Opus.csproj", "msbuildProject": "Meta.Voice.Opus.csproj"}, "Meta.Voice.VSDKHub.Editor/1.0.0": {"type": "project", "path": "Meta.Voice.VSDKHub.Editor.csproj", "msbuildProject": "Meta.Voice.VSDKHub.Editor.csproj"}, "Meta.VoiceSDK.Mic.Common/1.0.0": {"type": "project", "path": "Meta.VoiceSDK.Mic.Common.csproj", "msbuildProject": "Meta.VoiceSDK.Mic.Common.csproj"}, "Meta.VoiceSDK.Mic.Other/1.0.0": {"type": "project", "path": "Meta.VoiceSDK.Mic.Other.csproj", "msbuildProject": "Meta.VoiceSDK.Mic.Other.csproj"}, "Meta.VoiceSDK.Mic.WebGL/1.0.0": {"type": "project", "path": "Meta.VoiceSDK.Mic.WebGL.csproj", "msbuildProject": "Meta.VoiceSDK.Mic.WebGL.csproj"}, "Meta.Wit.Composer/1.0.0": {"type": "project", "path": "Meta.Wit.Composer.csproj", "msbuildProject": "Meta.Wit.Composer.csproj"}, "Meta.Wit.Composer.Editor/1.0.0": {"type": "project", "path": "Meta.Wit.Composer.Editor.csproj", "msbuildProject": "Meta.Wit.Composer.Editor.csproj"}, "Meta.Wit.Dictation/1.0.0": {"type": "project", "path": "Meta.Wit.Dictation.csproj", "msbuildProject": "Meta.Wit.Dictation.csproj"}, "Meta.Wit.Dictation.Editor/1.0.0": {"type": "project", "path": "Meta.Wit.Dictation.Editor.csproj", "msbuildProject": "Meta.Wit.Dictation.Editor.csproj"}, "Meta.WitAi/1.0.0": {"type": "project", "path": "Meta.WitAi.csproj", "msbuildProject": "Meta.WitAi.csproj"}, "Meta.WitAi.Editor/1.0.0": {"type": "project", "path": "Meta.WitAi.Editor.csproj", "msbuildProject": "Meta.WitAi.Editor.csproj"}, "Meta.WitAi.Lib/1.0.0": {"type": "project", "path": "Meta.WitAi.Lib.csproj", "msbuildProject": "Meta.WitAi.Lib.csproj"}, "Meta.WitAI.Lib.Editor/1.0.0": {"type": "project", "path": "Meta.WitAI.Lib.Editor.csproj", "msbuildProject": "Meta.WitAI.Lib.Editor.csproj"}, "Meta.WitAi.TTS/1.0.0": {"type": "project", "path": "Meta.WitAi.TTS.csproj", "msbuildProject": "Meta.WitAi.TTS.csproj"}, "Meta.WitAi.TTS.Editor/1.0.0": {"type": "project", "path": "Meta.WitAi.TTS.Editor.csproj", "msbuildProject": "Meta.WitAi.TTS.Editor.csproj"}, "Meta.XR.Audio/1.0.0": {"type": "project", "path": "Meta.XR.Audio.csproj", "msbuildProject": "Meta.XR.Audio.csproj"}, "Meta.XR.Audio.Editor/1.0.0": {"type": "project", "path": "Meta.XR.Audio.Editor.csproj", "msbuildProject": "Meta.XR.Audio.Editor.csproj"}, "Meta.XR.BuildingBlocks/1.0.0": {"type": "project", "path": "Meta.XR.BuildingBlocks.csproj", "msbuildProject": "Meta.XR.BuildingBlocks.csproj"}, "Meta.XR.BuildingBlocks.DepthAPI/1.0.0": {"type": "project", "path": "Meta.XR.BuildingBlocks.DepthAPI.csproj", "msbuildProject": "Meta.XR.BuildingBlocks.DepthAPI.csproj"}, "Meta.XR.BuildingBlocks.DepthAPI.Editor/1.0.0": {"type": "project", "path": "Meta.XR.BuildingBlocks.DepthAPI.Editor.csproj", "msbuildProject": "Meta.XR.BuildingBlocks.DepthAPI.Editor.csproj"}, "Meta.XR.BuildingBlocks.Editor/1.0.0": {"type": "project", "path": "Meta.XR.BuildingBlocks.Editor.csproj", "msbuildProject": "Meta.XR.BuildingBlocks.Editor.csproj"}, "Meta.XR.Editor.Callbacks/1.0.0": {"type": "project", "path": "Meta.XR.Editor.Callbacks.csproj", "msbuildProject": "Meta.XR.Editor.Callbacks.csproj"}, "Meta.XR.Editor.Guide.About/1.0.0": {"type": "project", "path": "Meta.XR.Editor.Guide.About.csproj", "msbuildProject": "Meta.XR.Editor.Guide.About.csproj"}, "Meta.XR.Editor.Id/1.0.0": {"type": "project", "path": "Meta.XR.Editor.Id.csproj", "msbuildProject": "Meta.XR.Editor.Id.csproj"}, "Meta.XR.Editor.Notifications/1.0.0": {"type": "project", "path": "Meta.XR.Editor.Notifications.csproj", "msbuildProject": "Meta.XR.Editor.Notifications.csproj"}, "Meta.XR.Editor.PlayCompanion/1.0.0": {"type": "project", "path": "Meta.XR.Editor.PlayCompanion.csproj", "msbuildProject": "Meta.XR.Editor.PlayCompanion.csproj"}, "Meta.XR.Editor.Reflection/1.0.0": {"type": "project", "path": "Meta.XR.Editor.Reflection.csproj", "msbuildProject": "Meta.XR.Editor.Reflection.csproj"}, "Meta.XR.Editor.Settings/1.0.0": {"type": "project", "path": "Meta.XR.Editor.Settings.csproj", "msbuildProject": "Meta.XR.Editor.Settings.csproj"}, "Meta.XR.Editor.StatusMenu/1.0.0": {"type": "project", "path": "Meta.XR.Editor.StatusMenu.csproj", "msbuildProject": "Meta.XR.Editor.StatusMenu.csproj"}, "Meta.XR.Editor.Tags/1.0.0": {"type": "project", "path": "Meta.XR.Editor.Tags.csproj", "msbuildProject": "Meta.XR.Editor.Tags.csproj"}, "Meta.XR.Editor.TelemetryUI/1.0.0": {"type": "project", "path": "Meta.XR.Editor.TelemetryUI.csproj", "msbuildProject": "Meta.XR.Editor.TelemetryUI.csproj"}, "Meta.XR.Editor.ToolingSupport/1.0.0": {"type": "project", "path": "Meta.XR.Editor.ToolingSupport.csproj", "msbuildProject": "Meta.XR.Editor.ToolingSupport.csproj"}, "Meta.XR.Editor.UPST.Notifications/1.0.0": {"type": "project", "path": "Meta.XR.Editor.UPST.Notifications.csproj", "msbuildProject": "Meta.XR.Editor.UPST.Notifications.csproj"}, "Meta.XR.Editor.UserInterface/1.0.0": {"type": "project", "path": "Meta.XR.Editor.UserInterface.csproj", "msbuildProject": "Meta.XR.Editor.UserInterface.csproj"}, "Meta.XR.EnvironmentDepth/1.0.0": {"type": "project", "path": "Meta.XR.EnvironmentDepth.csproj", "msbuildProject": "Meta.XR.EnvironmentDepth.csproj"}, "Meta.XR.EnvironmentDepth.Editor/1.0.0": {"type": "project", "path": "Meta.XR.EnvironmentDepth.Editor.csproj", "msbuildProject": "Meta.XR.EnvironmentDepth.Editor.csproj"}, "Meta.XR.Guides.Editor/1.0.0": {"type": "project", "path": "Meta.XR.Guides.Editor.csproj", "msbuildProject": "Meta.XR.Guides.Editor.csproj"}, "Meta.XR.ImmersiveDebugger/1.0.0": {"type": "project", "path": "Meta.XR.ImmersiveDebugger.csproj", "msbuildProject": "Meta.XR.ImmersiveDebugger.csproj"}, "Meta.XR.ImmersiveDebugger.Editor/1.0.0": {"type": "project", "path": "Meta.XR.ImmersiveDebugger.Editor.csproj", "msbuildProject": "Meta.XR.ImmersiveDebugger.Editor.csproj"}, "Meta.XR.ImmersiveDebugger.Interface/1.0.0": {"type": "project", "path": "Meta.XR.ImmersiveDebugger.Interface.csproj", "msbuildProject": "Meta.XR.ImmersiveDebugger.Interface.csproj"}, "meta.xr.mrutilitykit/1.0.0": {"type": "project", "path": "meta.xr.mrutilitykit.csproj", "msbuildProject": "meta.xr.mrutilitykit.csproj"}, "meta.xr.mrutilitykit.editor/1.0.0": {"type": "project", "path": "meta.xr.mrutilitykit.editor.csproj", "msbuildProject": "meta.xr.mrutilitykit.editor.csproj"}, "Meta.XR.MultiplayerBlocks.Fusion.Editor/1.0.0": {"type": "project", "path": "Meta.XR.MultiplayerBlocks.Fusion.Editor.csproj", "msbuildProject": "Meta.XR.MultiplayerBlocks.Fusion.Editor.csproj"}, "Meta.XR.MultiplayerBlocks.NGO.Editor/1.0.0": {"type": "project", "path": "Meta.XR.MultiplayerBlocks.NGO.Editor.csproj", "msbuildProject": "Meta.XR.MultiplayerBlocks.NGO.Editor.csproj"}, "Meta.XR.MultiplayerBlocks.Shared/1.0.0": {"type": "project", "path": "Meta.XR.MultiplayerBlocks.Shared.csproj", "msbuildProject": "Meta.XR.MultiplayerBlocks.Shared.csproj"}, "Meta.XR.MultiplayerBlocks.Shared.Editor/1.0.0": {"type": "project", "path": "Meta.XR.MultiplayerBlocks.Shared.Editor.csproj", "msbuildProject": "Meta.XR.MultiplayerBlocks.Shared.Editor.csproj"}, "MetaXrSimulator.Editor/1.0.0": {"type": "project", "path": "MetaXrSimulator.Editor.csproj", "msbuildProject": "MetaXrSimulator.Editor.csproj"}, "NaughtyAttributes.Core/1.0.0": {"type": "project", "path": "NaughtyAttributes.Core.csproj", "msbuildProject": "NaughtyAttributes.Core.csproj"}, "NaughtyAttributes.Editor/1.0.0": {"type": "project", "path": "NaughtyAttributes.Editor.csproj", "msbuildProject": "NaughtyAttributes.Editor.csproj"}, "NewAssembly/1.0.0": {"type": "project", "path": "NewAssembly.csproj", "msbuildProject": "NewAssembly.csproj"}, "Oculus.Haptics/1.0.0": {"type": "project", "path": "Oculus.Haptics.csproj", "msbuildProject": "Oculus.Haptics.csproj"}, "Oculus.Haptics.Editor/1.0.0": {"type": "project", "path": "Oculus.Haptics.Editor.csproj", "msbuildProject": "Oculus.Haptics.Editor.csproj"}, "Oculus.Interaction/1.0.0": {"type": "project", "path": "Oculus.Interaction.csproj", "msbuildProject": "Oculus.Interaction.csproj"}, "Oculus.Interaction.Editor/1.0.0": {"type": "project", "path": "Oculus.Interaction.Editor.csproj", "msbuildProject": "Oculus.Interaction.Editor.csproj"}, "Oculus.Interaction.Editor.UnityXR/1.0.0": {"type": "project", "path": "Oculus.Interaction.Editor.UnityXR.csproj", "msbuildProject": "Oculus.Interaction.Editor.UnityXR.csproj"}, "Oculus.Interaction.InterfaceSupport/1.0.0": {"type": "project", "path": "Oculus.Interaction.InterfaceSupport.csproj", "msbuildProject": "Oculus.Interaction.InterfaceSupport.csproj"}, "Oculus.Interaction.OVR/1.0.0": {"type": "project", "path": "Oculus.Interaction.OVR.csproj", "msbuildProject": "Oculus.Interaction.OVR.csproj"}, "Oculus.Interaction.OVR.Editor/1.0.0": {"type": "project", "path": "Oculus.Interaction.OVR.Editor.csproj", "msbuildProject": "Oculus.Interaction.OVR.Editor.csproj"}, "Oculus.Interaction.OVR.Samples/1.0.0": {"type": "project", "path": "Oculus.Interaction.OVR.Samples.csproj", "msbuildProject": "Oculus.Interaction.OVR.Samples.csproj"}, "Oculus.Interaction.Samples/1.0.0": {"type": "project", "path": "Oculus.Interaction.Samples.csproj", "msbuildProject": "Oculus.Interaction.Samples.csproj"}, "Oculus.Interaction.Samples.Editor/1.0.0": {"type": "project", "path": "Oculus.Interaction.Samples.Editor.csproj", "msbuildProject": "Oculus.Interaction.Samples.Editor.csproj"}, "Oculus.Interaction.UnityXR/1.0.0": {"type": "project", "path": "Oculus.Interaction.UnityXR.csproj", "msbuildProject": "Oculus.Interaction.UnityXR.csproj"}, "Oculus.Platform/1.0.0": {"type": "project", "path": "Oculus.Platform.csproj", "msbuildProject": "Oculus.Platform.csproj"}, "Oculus.Platform.Editor/1.0.0": {"type": "project", "path": "Oculus.Platform.Editor.csproj", "msbuildProject": "Oculus.Platform.Editor.csproj"}, "Oculus.VR/1.0.0": {"type": "project", "path": "Oculus.VR.csproj", "msbuildProject": "Oculus.VR.csproj"}, "Oculus.VR.Editor/1.0.0": {"type": "project", "path": "Oculus.VR.Editor.csproj", "msbuildProject": "Oculus.VR.Editor.csproj"}, "Oculus.VR.Scripts.Editor/1.0.0": {"type": "project", "path": "Oculus.VR.Scripts.Editor.csproj", "msbuildProject": "Oculus.VR.Scripts.Editor.csproj"}, "PPv2URPConverters/1.0.0": {"type": "project", "path": "PPv2URPConverters.csproj", "msbuildProject": "PPv2URPConverters.csproj"}, "RootMotion/1.0.0": {"type": "project", "path": "RootMotion.csproj", "msbuildProject": "RootMotion.csproj"}, "RootMotionEditor/1.0.0": {"type": "project", "path": "RootMotionEditor.csproj", "msbuildProject": "RootMotionEditor.csproj"}, "Unity.AI.Navigation/1.0.0": {"type": "project", "path": "Unity.AI.Navigation.csproj", "msbuildProject": "Unity.AI.Navigation.csproj"}, "Unity.AI.Navigation.Editor/1.0.0": {"type": "project", "path": "Unity.AI.Navigation.Editor.csproj", "msbuildProject": "Unity.AI.Navigation.Editor.csproj"}, "Unity.AI.Navigation.Editor.ConversionSystem/1.0.0": {"type": "project", "path": "Unity.AI.Navigation.Editor.ConversionSystem.csproj", "msbuildProject": "Unity.AI.Navigation.Editor.ConversionSystem.csproj"}, "Unity.AI.Navigation.Updater/1.0.0": {"type": "project", "path": "Unity.AI.Navigation.Updater.csproj", "msbuildProject": "Unity.AI.Navigation.Updater.csproj"}, "Unity.Burst/1.0.0": {"type": "project", "path": "Unity.Burst.csproj", "msbuildProject": "Unity.Burst.csproj"}, "Unity.Burst.Editor/1.0.0": {"type": "project", "path": "Unity.Burst.Editor.csproj", "msbuildProject": "Unity.Burst.Editor.csproj"}, "Unity.Collections/1.0.0": {"type": "project", "path": "Unity.Collections.csproj", "msbuildProject": "Unity.Collections.csproj"}, "Unity.Collections.Editor/1.0.0": {"type": "project", "path": "Unity.Collections.Editor.csproj", "msbuildProject": "Unity.Collections.Editor.csproj"}, "Unity.InputSystem/1.0.0": {"type": "project", "path": "Unity.InputSystem.csproj", "msbuildProject": "Unity.InputSystem.csproj"}, "Unity.InputSystem.ForUI/1.0.0": {"type": "project", "path": "Unity.InputSystem.ForUI.csproj", "msbuildProject": "Unity.InputSystem.ForUI.csproj"}, "Unity.Mathematics/1.0.0": {"type": "project", "path": "Unity.Mathematics.csproj", "msbuildProject": "Unity.Mathematics.csproj"}, "Unity.Mathematics.Editor/1.0.0": {"type": "project", "path": "Unity.Mathematics.Editor.csproj", "msbuildProject": "Unity.Mathematics.Editor.csproj"}, "Unity.Multiplayer.Center.Common/1.0.0": {"type": "project", "path": "Unity.Multiplayer.Center.Common.csproj", "msbuildProject": "Unity.Multiplayer.Center.Common.csproj"}, "Unity.Multiplayer.Center.Editor/1.0.0": {"type": "project", "path": "Unity.Multiplayer.Center.Editor.csproj", "msbuildProject": "Unity.Multiplayer.Center.Editor.csproj"}, "Unity.PlasticSCM.Editor/1.0.0": {"type": "project", "path": "Unity.PlasticSCM.Editor.csproj", "msbuildProject": "Unity.PlasticSCM.Editor.csproj"}, "Unity.Rendering.LightTransport.Editor/1.0.0": {"type": "project", "path": "Unity.Rendering.LightTransport.Editor.csproj", "msbuildProject": "Unity.Rendering.LightTransport.Editor.csproj"}, "Unity.Rendering.LightTransport.Runtime/1.0.0": {"type": "project", "path": "Unity.Rendering.LightTransport.Runtime.csproj", "msbuildProject": "Unity.Rendering.LightTransport.Runtime.csproj"}, "Unity.RenderPipeline.Universal.ShaderLibrary/1.0.0": {"type": "project", "path": "Unity.RenderPipeline.Universal.ShaderLibrary.csproj", "msbuildProject": "Unity.RenderPipeline.Universal.ShaderLibrary.csproj"}, "Unity.RenderPipelines.Core.Editor/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Editor.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Editor.csproj"}, "Unity.RenderPipelines.Core.Editor.Shared/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Editor.Shared.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Editor.Shared.csproj"}, "Unity.RenderPipelines.Core.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Runtime.csproj"}, "Unity.RenderPipelines.Core.Runtime.Shared/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Runtime.Shared.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Runtime.Shared.csproj"}, "Unity.RenderPipelines.Core.ShaderLibrary/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.ShaderLibrary.csproj", "msbuildProject": "Unity.RenderPipelines.Core.ShaderLibrary.csproj"}, "Unity.RenderPipelines.GPUDriven.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.GPUDriven.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.GPUDriven.Runtime.csproj"}, "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj", "msbuildProject": "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj"}, "Unity.RenderPipelines.Universal.2D.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.2D.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.2D.Runtime.csproj"}, "Unity.RenderPipelines.Universal.Config.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.Config.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.Config.Runtime.csproj"}, "Unity.RenderPipelines.Universal.Editor/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.Editor.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.Editor.csproj"}, "Unity.RenderPipelines.Universal.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.Runtime.csproj"}, "Unity.RenderPipelines.Universal.Shaders/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.Shaders.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.Shaders.csproj"}, "Unity.Rider.Editor/1.0.0": {"type": "project", "path": "Unity.Rider.Editor.csproj", "msbuildProject": "Unity.Rider.Editor.csproj"}, "Unity.Searcher.Editor/1.0.0": {"type": "project", "path": "Unity.Searcher.Editor.csproj", "msbuildProject": "Unity.Searcher.Editor.csproj"}, "Unity.ShaderGraph.Editor/1.0.0": {"type": "project", "path": "Unity.ShaderGraph.Editor.csproj", "msbuildProject": "Unity.ShaderGraph.Editor.csproj"}, "Unity.ShaderGraph.Utilities/1.0.0": {"type": "project", "path": "Unity.ShaderGraph.Utilities.csproj", "msbuildProject": "Unity.ShaderGraph.Utilities.csproj"}, "Unity.TextMeshPro/1.0.0": {"type": "project", "path": "Unity.TextMeshPro.csproj", "msbuildProject": "Unity.TextMeshPro.csproj"}, "Unity.TextMeshPro.Editor/1.0.0": {"type": "project", "path": "Unity.TextMeshPro.Editor.csproj", "msbuildProject": "Unity.TextMeshPro.Editor.csproj"}, "Unity.Timeline/1.0.0": {"type": "project", "path": "Unity.Timeline.csproj", "msbuildProject": "Unity.Timeline.csproj"}, "Unity.Timeline.Editor/1.0.0": {"type": "project", "path": "Unity.Timeline.Editor.csproj", "msbuildProject": "Unity.Timeline.Editor.csproj"}, "Unity.VisualScripting.Core/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Core.csproj", "msbuildProject": "Unity.VisualScripting.Core.csproj"}, "Unity.VisualScripting.Core.Editor/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Core.Editor.csproj", "msbuildProject": "Unity.VisualScripting.Core.Editor.csproj"}, "Unity.VisualScripting.Flow/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Flow.csproj", "msbuildProject": "Unity.VisualScripting.Flow.csproj"}, "Unity.VisualScripting.Flow.Editor/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Flow.Editor.csproj", "msbuildProject": "Unity.VisualScripting.Flow.Editor.csproj"}, "Unity.VisualScripting.SettingsProvider.Editor/1.0.0": {"type": "project", "path": "Unity.VisualScripting.SettingsProvider.Editor.csproj", "msbuildProject": "Unity.VisualScripting.SettingsProvider.Editor.csproj"}, "Unity.VisualScripting.Shared.Editor/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Shared.Editor.csproj", "msbuildProject": "Unity.VisualScripting.Shared.Editor.csproj"}, "Unity.VisualScripting.State/1.0.0": {"type": "project", "path": "Unity.VisualScripting.State.csproj", "msbuildProject": "Unity.VisualScripting.State.csproj"}, "Unity.VisualScripting.State.Editor/1.0.0": {"type": "project", "path": "Unity.VisualScripting.State.Editor.csproj", "msbuildProject": "Unity.VisualScripting.State.Editor.csproj"}, "Unity.VisualStudio.Editor/1.0.0": {"type": "project", "path": "Unity.VisualStudio.Editor.csproj", "msbuildProject": "Unity.VisualStudio.Editor.csproj"}, "Unity.XR.CoreUtils/1.0.0": {"type": "project", "path": "Unity.XR.CoreUtils.csproj", "msbuildProject": "Unity.XR.CoreUtils.csproj"}, "Unity.XR.CoreUtils.Editor/1.0.0": {"type": "project", "path": "Unity.XR.CoreUtils.Editor.csproj", "msbuildProject": "Unity.XR.CoreUtils.Editor.csproj"}, "Unity.XR.Management/1.0.0": {"type": "project", "path": "Unity.XR.Management.csproj", "msbuildProject": "Unity.XR.Management.csproj"}, "Unity.XR.Management.Editor/1.0.0": {"type": "project", "path": "Unity.XR.Management.Editor.csproj", "msbuildProject": "Unity.XR.Management.Editor.csproj"}, "Unity.XR.Oculus/1.0.0": {"type": "project", "path": "Unity.XR.Oculus.csproj", "msbuildProject": "Unity.XR.Oculus.csproj"}, "Unity.XR.Oculus.Editor/1.0.0": {"type": "project", "path": "Unity.XR.Oculus.Editor.csproj", "msbuildProject": "Unity.XR.Oculus.Editor.csproj"}, "Unity.XR.OpenXR/1.0.0": {"type": "project", "path": "Unity.XR.OpenXR.csproj", "msbuildProject": "Unity.XR.OpenXR.csproj"}, "Unity.XR.OpenXR.Editor/1.0.0": {"type": "project", "path": "Unity.XR.OpenXR.Editor.csproj", "msbuildProject": "Unity.XR.OpenXR.Editor.csproj"}, "Unity.XR.OpenXR.Features.ConformanceAutomation/1.0.0": {"type": "project", "path": "Unity.XR.OpenXR.Features.ConformanceAutomation.csproj", "msbuildProject": "Unity.XR.OpenXR.Features.ConformanceAutomation.csproj"}, "Unity.XR.OpenXR.Features.MetaQuestSupport/1.0.0": {"type": "project", "path": "Unity.XR.OpenXR.Features.MetaQuestSupport.csproj", "msbuildProject": "Unity.XR.OpenXR.Features.MetaQuestSupport.csproj"}, "Unity.XR.OpenXR.Features.MetaQuestSupport.Editor/1.0.0": {"type": "project", "path": "Unity.XR.OpenXR.Features.MetaQuestSupport.Editor.csproj", "msbuildProject": "Unity.XR.OpenXR.Features.MetaQuestSupport.Editor.csproj"}, "Unity.XR.OpenXR.Features.OculusQuestSupport/1.0.0": {"type": "project", "path": "Unity.XR.OpenXR.Features.OculusQuestSupport.csproj", "msbuildProject": "Unity.XR.OpenXR.Features.OculusQuestSupport.csproj"}, "Unity.XR.OpenXR.Features.OculusQuestSupport.Editor/1.0.0": {"type": "project", "path": "Unity.XR.OpenXR.Features.OculusQuestSupport.Editor.csproj", "msbuildProject": "Unity.XR.OpenXR.Features.OculusQuestSupport.Editor.csproj"}, "Unity.XR.OpenXR.Features.RuntimeDebugger/1.0.0": {"type": "project", "path": "Unity.XR.OpenXR.Features.RuntimeDebugger.csproj", "msbuildProject": "Unity.XR.OpenXR.Features.RuntimeDebugger.csproj"}, "Unity.XR.OpenXR.Features.RuntimeDebugger.Editor/1.0.0": {"type": "project", "path": "Unity.XR.OpenXR.Features.RuntimeDebugger.Editor.csproj", "msbuildProject": "Unity.XR.OpenXR.Features.RuntimeDebugger.Editor.csproj"}, "UnityEditor.SpatialTracking/1.0.0": {"type": "project", "path": "UnityEditor.SpatialTracking.csproj", "msbuildProject": "UnityEditor.SpatialTracking.csproj"}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "path": "UnityEditor.TestRunner.csproj", "msbuildProject": "UnityEditor.TestRunner.csproj"}, "UnityEditor.UI/1.0.0": {"type": "project", "path": "UnityEditor.UI.csproj", "msbuildProject": "UnityEditor.UI.csproj"}, "UnityEditor.XR.LegacyInputHelpers/1.0.0": {"type": "project", "path": "UnityEditor.XR.LegacyInputHelpers.csproj", "msbuildProject": "UnityEditor.XR.LegacyInputHelpers.csproj"}, "UnityEngine.SpatialTracking/1.0.0": {"type": "project", "path": "UnityEngine.SpatialTracking.csproj", "msbuildProject": "UnityEngine.SpatialTracking.csproj"}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "path": "UnityEngine.TestRunner.csproj", "msbuildProject": "UnityEngine.TestRunner.csproj"}, "UnityEngine.UI/1.0.0": {"type": "project", "path": "UnityEngine.UI.csproj", "msbuildProject": "UnityEngine.UI.csproj"}, "UnityEngine.XR.LegacyInputHelpers/1.0.0": {"type": "project", "path": "UnityEngine.XR.LegacyInputHelpers.csproj", "msbuildProject": "UnityEngine.XR.LegacyInputHelpers.csproj"}, "VoiceSDK.Dictation.Editor/1.0.0": {"type": "project", "path": "VoiceSDK.Dictation.Editor.csproj", "msbuildProject": "VoiceSDK.Dictation.Editor.csproj"}, "VoiceSDK.Dictation.Runtime/1.0.0": {"type": "project", "path": "VoiceSDK.Dictation.Runtime.csproj", "msbuildProject": "VoiceSDK.Dictation.Runtime.csproj"}, "VoiceSDK.Editor/1.0.0": {"type": "project", "path": "VoiceSDK.Editor.csproj", "msbuildProject": "VoiceSDK.Editor.csproj"}, "VoiceSDK.Editor.Composer/1.0.0": {"type": "project", "path": "VoiceSDK.Editor.Composer.csproj", "msbuildProject": "VoiceSDK.Editor.Composer.csproj"}, "VoiceSDK.Runtime/1.0.0": {"type": "project", "path": "VoiceSDK.Runtime.csproj", "msbuildProject": "VoiceSDK.Runtime.csproj"}, "VoiceSDK.Runtime.Composer/1.0.0": {"type": "project", "path": "VoiceSDK.Runtime.Composer.csproj", "msbuildProject": "VoiceSDK.Runtime.Composer.csproj"}, "VoiceSDK.Telemetry/1.0.0": {"type": "project", "path": "VoiceSDK.Telemetry.csproj", "msbuildProject": "VoiceSDK.Telemetry.csproj"}, "VoiceSDK.Telemetry.Runtime/1.0.0": {"type": "project", "path": "VoiceSDK.Telemetry.Runtime.csproj", "msbuildProject": "VoiceSDK.Telemetry.Runtime.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Assembly-CSharp-firstpass >= 1.0.0", "AssistantCoreSDKEditor >= 1.0.0", "AssistantCoreSDKRuntime >= 1.0.0", "AssistantVoiceCommandCommon >= 1.0.0", "AssistantVoiceCommandCommon.Editor >= 1.0.0", "AutoHandAssembly >= 1.0.0", "Autohand.Editor >= 1.0.0", "Autohand.MetaXR >= 1.0.0", "Autohand.MetaXR.Editor >= 1.0.0", "Autohand.XR >= 1.0.0", "Autohand.XR.Editor >= 1.0.0", "Meta.Net.endel.nativewebsocket >= 1.0.0", "Meta.Voice.Hub.Editor >= 1.0.0", "Meta.Voice.Hub.Runtime >= 1.0.0", "Meta.Voice.NLayer >= 1.0.0", "Meta.Voice.Opus >= 1.0.0", "Meta.Voice.VSDKHub.Editor >= 1.0.0", "Meta.VoiceSDK.Mic.Common >= 1.0.0", "Meta.VoiceSDK.Mic.Other >= 1.0.0", "Meta.VoiceSDK.Mic.WebGL >= 1.0.0", "Meta.Wit.Composer >= 1.0.0", "Meta.Wit.Composer.Editor >= 1.0.0", "Meta.Wit.Dictation >= 1.0.0", "Meta.Wit.Dictation.Editor >= 1.0.0", "Meta.WitAI.Lib.Editor >= 1.0.0", "Meta.WitAi >= 1.0.0", "Meta.WitAi.Editor >= 1.0.0", "Meta.WitAi.Lib >= 1.0.0", "Meta.WitAi.TTS >= 1.0.0", "Meta.WitAi.TTS.Editor >= 1.0.0", "Meta.XR.Audio >= 1.0.0", "Meta.XR.Audio.Editor >= 1.0.0", "Meta.XR.Building<PERSON>locks >= 1.0.0", "Meta.XR.BuildingBlocks.DepthAPI >= 1.0.0", "Meta.XR.BuildingBlocks.DepthAPI.Editor >= 1.0.0", "Meta.XR.BuildingBlocks.Editor >= 1.0.0", "Meta.XR.Editor.Callbacks >= 1.0.0", "Meta.XR.Editor.Guide.About >= 1.0.0", "Meta.XR.Editor.Id >= 1.0.0", "Meta.XR.Editor.Notifications >= 1.0.0", "Meta.XR.Editor.PlayCompanion >= 1.0.0", "Meta.XR.Editor.Reflection >= 1.0.0", "Meta.XR.Editor.Settings >= 1.0.0", "Meta.XR.Editor.StatusMenu >= 1.0.0", "Meta.XR.Editor.Tags >= 1.0.0", "Meta.XR.Editor.TelemetryUI >= 1.0.0", "Meta.XR.Editor.ToolingSupport >= 1.0.0", "Meta.XR.Editor.UPST.Notifications >= 1.0.0", "Meta.XR.Editor.UserInterface >= 1.0.0", "Meta.XR.EnvironmentDepth >= 1.0.0", "Meta.XR.EnvironmentDepth.Editor >= 1.0.0", "Meta.XR.Guides.Editor >= 1.0.0", "Meta.XR.<PERSON>ebugger >= 1.0.0", "Meta.XR.ImmersiveDebugger.Editor >= 1.0.0", "Meta.XR.ImmersiveDebugger.Interface >= 1.0.0", "Meta.XR.MultiplayerBlocks.Fusion.Editor >= 1.0.0", "Meta.XR.MultiplayerBlocks.NGO.Editor >= 1.0.0", "Meta.XR.MultiplayerBlocks.Shared >= 1.0.0", "Meta.XR.MultiplayerBlocks.Shared.Editor >= 1.0.0", "MetaXrSimulator.Editor >= 1.0.0", "NaughtyAttributes.Core >= 1.0.0", "NaughtyAttributes.Editor >= 1.0.0", "NewAssembly >= 1.0.0", "Oculus.Haptics >= 1.0.0", "Oculus.Haptics.Editor >= 1.0.0", "Oculus.Interaction >= 1.0.0", "Oculus.Interaction.Editor >= 1.0.0", "Oculus.Interaction.Editor.UnityXR >= 1.0.0", "Oculus.Interaction.InterfaceSupport >= 1.0.0", "Oculus.Interaction.OVR >= 1.0.0", "Oculus.Interaction.OVR.Editor >= 1.0.0", "Oculus.Interaction.OVR.Samples >= 1.0.0", "Oculus.Interaction.Samples >= 1.0.0", "Oculus.Interaction.Samples.Editor >= 1.0.0", "Oculus.Interaction.UnityXR >= 1.0.0", "Oculus.Platform >= 1.0.0", "Oculus.Platform.Editor >= 1.0.0", "Oculus.VR >= 1.0.0", "Oculus.VR.Editor >= 1.0.0", "Oculus.VR.Sc<PERSON>ts.Editor >= 1.0.0", "PPv2URPConverters >= 1.0.0", "RootMotion >= 1.0.0", "RootMotionEditor >= 1.0.0", "Unity.AI.Navigation >= 1.0.0", "Unity.AI.Navigation.Editor >= 1.0.0", "Unity.AI.Navigation.Editor.ConversionSystem >= 1.0.0", "Unity.AI.Navigation.Updater >= 1.0.0", "Unity.Burst >= 1.0.0", "Unity.Burst.Editor >= 1.0.0", "Unity.Collections >= 1.0.0", "Unity.Collections.Editor >= 1.0.0", "Unity.InputSystem >= 1.0.0", "Unity.InputSystem.ForUI >= 1.0.0", "Unity.Mathematics >= 1.0.0", "Unity.Mathematics.Editor >= 1.0.0", "Unity.Multiplayer.Center.Common >= 1.0.0", "Unity.Multiplayer.Center.Editor >= 1.0.0", "Unity.PlasticSCM.Editor >= 1.0.0", "Unity.RenderPipeline.Universal.ShaderLibrary >= 1.0.0", "Unity.RenderPipelines.Core.Editor >= 1.0.0", "Unity.RenderPipelines.Core.Editor.Shared >= 1.0.0", "Unity.RenderPipelines.Core.Runtime >= 1.0.0", "Unity.RenderPipelines.Core.Runtime.Shared >= 1.0.0", "Unity.RenderPipelines.Core.ShaderLibrary >= 1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime >= 1.0.0", "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary >= 1.0.0", "Unity.RenderPipelines.Universal.2D.Runtime >= 1.0.0", "Unity.RenderPipelines.Universal.Config.Runtime >= 1.0.0", "Unity.RenderPipelines.Universal.Editor >= 1.0.0", "Unity.RenderPipelines.Universal.Runtime >= 1.0.0", "Unity.RenderPipelines.Universal.Shaders >= 1.0.0", "Unity.Rendering.LightTransport.Editor >= 1.0.0", "Unity.Rendering.LightTransport.Runtime >= 1.0.0", "Unity.Rider.Editor >= 1.0.0", "Unity.Searcher.Editor >= 1.0.0", "Unity.ShaderGraph.Editor >= 1.0.0", "Unity.TextMeshPro >= 1.0.0", "Unity.TextMeshPro.Editor >= 1.0.0", "Unity.Timeline >= 1.0.0", "Unity.Timeline.Editor >= 1.0.0", "Unity.VisualScripting.Core >= 1.0.0", "Unity.VisualScripting.Core.Editor >= 1.0.0", "Unity.VisualScripting.Flow >= 1.0.0", "Unity.VisualScripting.Flow.Editor >= 1.0.0", "Unity.VisualScripting.SettingsProvider.Editor >= 1.0.0", "Unity.VisualScripting.Shared.Editor >= 1.0.0", "Unity.VisualScripting.State >= 1.0.0", "Unity.VisualScripting.State.Editor >= 1.0.0", "Unity.VisualStudio.Editor >= 1.0.0", "Unity.XR.CoreUtils >= 1.0.0", "Unity.XR.CoreUtils.Editor >= 1.0.0", "Unity.XR.Management >= 1.0.0", "Unity.XR.Management.Editor >= 1.0.0", "Unity.XR.Oculus >= 1.0.0", "Unity.XR.Oculus.Editor >= 1.0.0", "Unity.XR.OpenXR >= 1.0.0", "Unity.XR.OpenXR.Editor >= 1.0.0", "Unity.XR.OpenXR.Features.ConformanceAutomation >= 1.0.0", "Unity.XR.OpenXR.Features.MetaQuestSupport >= 1.0.0", "Unity.XR.OpenXR.Features.MetaQuestSupport.Editor >= 1.0.0", "Unity.XR.OpenXR.Features.OculusQuestSupport >= 1.0.0", "Unity.XR.OpenXR.Features.OculusQuestSupport.Editor >= 1.0.0", "Unity.XR.OpenXR.Features.RuntimeDebugger.Editor >= 1.0.0", "UnityEditor.SpatialTracking >= 1.0.0", "UnityEditor.TestRunner >= 1.0.0", "UnityEditor.UI >= 1.0.0", "UnityEditor.XR.LegacyInputHelpers >= 1.0.0", "UnityEngine.SpatialTracking >= 1.0.0", "UnityEngine.TestRunner >= 1.0.0", "UnityEngine.UI >= 1.0.0", "UnityEngine.XR.LegacyInputHelpers >= 1.0.0", "VoiceSDK.Dictation.Editor >= 1.0.0", "VoiceSDK.Dictation.Runtime >= 1.0.0", "VoiceSDK.Editor >= 1.0.0", "VoiceSDK.Editor.Composer >= 1.0.0", "VoiceSDK.Runtime >= 1.0.0", "VoiceSDK.Runtime.Composer >= 1.0.0", "VoiceSDK.Telemetry >= 1.0.0", "VoiceSDK.Telemetry.Runtime >= 1.0.0", "meta.xr.mrutilitykit >= 1.0.0", "meta.xr.mrutilitykit.editor >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VRTesting\\Assembly-CSharp-Editor-firstpass.csproj", "projectName": "Assembly-<PERSON><PERSON><PERSON>-Editor-first<PERSON>", "projectPath": "D:\\Projects\\VRTesting\\Assembly-CSharp-Editor-firstpass.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VRTesting\\Temp\\obj\\Assembly-CSharp-Editor-firstpass\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Projects\\VRTesting\\Assembly-CSharp-firstpass.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Assembly-CSharp-firstpass.csproj"}, "D:\\Projects\\VRTesting\\AssistantCoreSDKEditor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\AssistantCoreSDKEditor.csproj"}, "D:\\Projects\\VRTesting\\AssistantCoreSDKRuntime.csproj": {"projectPath": "D:\\Projects\\VRTesting\\AssistantCoreSDKRuntime.csproj"}, "D:\\Projects\\VRTesting\\AssistantVoiceCommandCommon.csproj": {"projectPath": "D:\\Projects\\VRTesting\\AssistantVoiceCommandCommon.csproj"}, "D:\\Projects\\VRTesting\\AssistantVoiceCommandCommon.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\AssistantVoiceCommandCommon.Editor.csproj"}, "D:\\Projects\\VRTesting\\Autohand.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Autohand.Editor.csproj"}, "D:\\Projects\\VRTesting\\Autohand.MetaXR.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Autohand.MetaXR.csproj"}, "D:\\Projects\\VRTesting\\Autohand.MetaXR.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Autohand.MetaXR.Editor.csproj"}, "D:\\Projects\\VRTesting\\Autohand.XR.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Autohand.XR.csproj"}, "D:\\Projects\\VRTesting\\Autohand.XR.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Autohand.XR.Editor.csproj"}, "D:\\Projects\\VRTesting\\AutoHandAssembly.csproj": {"projectPath": "D:\\Projects\\VRTesting\\AutoHandAssembly.csproj"}, "D:\\Projects\\VRTesting\\Meta.Net.endel.nativewebsocket.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.Net.endel.nativewebsocket.csproj"}, "D:\\Projects\\VRTesting\\Meta.Voice.Hub.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.Voice.Hub.Editor.csproj"}, "D:\\Projects\\VRTesting\\Meta.Voice.Hub.Runtime.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.Voice.Hub.Runtime.csproj"}, "D:\\Projects\\VRTesting\\Meta.Voice.NLayer.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.Voice.NLayer.csproj"}, "D:\\Projects\\VRTesting\\Meta.Voice.Opus.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.Voice.Opus.csproj"}, "D:\\Projects\\VRTesting\\Meta.Voice.VSDKHub.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.Voice.VSDKHub.Editor.csproj"}, "D:\\Projects\\VRTesting\\Meta.VoiceSDK.Mic.Common.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.VoiceSDK.Mic.Common.csproj"}, "D:\\Projects\\VRTesting\\Meta.VoiceSDK.Mic.Other.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.VoiceSDK.Mic.Other.csproj"}, "D:\\Projects\\VRTesting\\Meta.VoiceSDK.Mic.WebGL.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.VoiceSDK.Mic.WebGL.csproj"}, "D:\\Projects\\VRTesting\\Meta.Wit.Composer.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.Wit.Composer.csproj"}, "D:\\Projects\\VRTesting\\Meta.Wit.Composer.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.Wit.Composer.Editor.csproj"}, "D:\\Projects\\VRTesting\\Meta.Wit.Dictation.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.Wit.Dictation.csproj"}, "D:\\Projects\\VRTesting\\Meta.Wit.Dictation.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.Wit.Dictation.Editor.csproj"}, "D:\\Projects\\VRTesting\\Meta.WitAi.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.WitAi.csproj"}, "D:\\Projects\\VRTesting\\Meta.WitAi.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.WitAi.Editor.csproj"}, "D:\\Projects\\VRTesting\\Meta.WitAi.Lib.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.WitAi.Lib.csproj"}, "D:\\Projects\\VRTesting\\Meta.WitAI.Lib.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.WitAI.Lib.Editor.csproj"}, "D:\\Projects\\VRTesting\\Meta.WitAi.TTS.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.WitAi.TTS.csproj"}, "D:\\Projects\\VRTesting\\Meta.WitAi.TTS.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.WitAi.TTS.Editor.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.Audio.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.Audio.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.Audio.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.Audio.Editor.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.BuildingBlocks.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.BuildingBlocks.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.BuildingBlocks.DepthAPI.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.BuildingBlocks.DepthAPI.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.BuildingBlocks.DepthAPI.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.BuildingBlocks.DepthAPI.Editor.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.BuildingBlocks.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.BuildingBlocks.Editor.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.Editor.Callbacks.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.Editor.Callbacks.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.Editor.Guide.About.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.Editor.Guide.About.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.Editor.Id.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.Editor.Id.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.Editor.Notifications.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.Editor.Notifications.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.Editor.PlayCompanion.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.Editor.PlayCompanion.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.Editor.Reflection.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.Editor.Reflection.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.Editor.Settings.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.Editor.Settings.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.Editor.StatusMenu.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.Editor.StatusMenu.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.Editor.Tags.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.Editor.Tags.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.Editor.TelemetryUI.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.Editor.TelemetryUI.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.Editor.ToolingSupport.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.Editor.ToolingSupport.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.Editor.UPST.Notifications.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.Editor.UPST.Notifications.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.Editor.UserInterface.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.Editor.UserInterface.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.EnvironmentDepth.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.EnvironmentDepth.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.EnvironmentDepth.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.EnvironmentDepth.Editor.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.Guides.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.Guides.Editor.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.ImmersiveDebugger.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.ImmersiveDebugger.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.ImmersiveDebugger.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.ImmersiveDebugger.Editor.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.ImmersiveDebugger.Interface.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.ImmersiveDebugger.Interface.csproj"}, "D:\\Projects\\VRTesting\\meta.xr.mrutilitykit.csproj": {"projectPath": "D:\\Projects\\VRTesting\\meta.xr.mrutilitykit.csproj"}, "D:\\Projects\\VRTesting\\meta.xr.mrutilitykit.editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\meta.xr.mrutilitykit.editor.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.MultiplayerBlocks.Fusion.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.MultiplayerBlocks.Fusion.Editor.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.MultiplayerBlocks.NGO.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.MultiplayerBlocks.NGO.Editor.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.MultiplayerBlocks.Shared.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.MultiplayerBlocks.Shared.csproj"}, "D:\\Projects\\VRTesting\\Meta.XR.MultiplayerBlocks.Shared.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Meta.XR.MultiplayerBlocks.Shared.Editor.csproj"}, "D:\\Projects\\VRTesting\\MetaXrSimulator.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\MetaXrSimulator.Editor.csproj"}, "D:\\Projects\\VRTesting\\NaughtyAttributes.Core.csproj": {"projectPath": "D:\\Projects\\VRTesting\\NaughtyAttributes.Core.csproj"}, "D:\\Projects\\VRTesting\\NaughtyAttributes.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\NaughtyAttributes.Editor.csproj"}, "D:\\Projects\\VRTesting\\NewAssembly.csproj": {"projectPath": "D:\\Projects\\VRTesting\\NewAssembly.csproj"}, "D:\\Projects\\VRTesting\\Oculus.Haptics.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Oculus.Haptics.csproj"}, "D:\\Projects\\VRTesting\\Oculus.Haptics.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Oculus.Haptics.Editor.csproj"}, "D:\\Projects\\VRTesting\\Oculus.Interaction.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Oculus.Interaction.csproj"}, "D:\\Projects\\VRTesting\\Oculus.Interaction.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Oculus.Interaction.Editor.csproj"}, "D:\\Projects\\VRTesting\\Oculus.Interaction.Editor.UnityXR.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Oculus.Interaction.Editor.UnityXR.csproj"}, "D:\\Projects\\VRTesting\\Oculus.Interaction.InterfaceSupport.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Oculus.Interaction.InterfaceSupport.csproj"}, "D:\\Projects\\VRTesting\\Oculus.Interaction.OVR.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Oculus.Interaction.OVR.csproj"}, "D:\\Projects\\VRTesting\\Oculus.Interaction.OVR.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Oculus.Interaction.OVR.Editor.csproj"}, "D:\\Projects\\VRTesting\\Oculus.Interaction.OVR.Samples.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Oculus.Interaction.OVR.Samples.csproj"}, "D:\\Projects\\VRTesting\\Oculus.Interaction.Samples.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Oculus.Interaction.Samples.csproj"}, "D:\\Projects\\VRTesting\\Oculus.Interaction.Samples.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Oculus.Interaction.Samples.Editor.csproj"}, "D:\\Projects\\VRTesting\\Oculus.Interaction.UnityXR.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Oculus.Interaction.UnityXR.csproj"}, "D:\\Projects\\VRTesting\\Oculus.Platform.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Oculus.Platform.csproj"}, "D:\\Projects\\VRTesting\\Oculus.Platform.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Oculus.Platform.Editor.csproj"}, "D:\\Projects\\VRTesting\\Oculus.VR.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Oculus.VR.csproj"}, "D:\\Projects\\VRTesting\\Oculus.VR.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Oculus.VR.Editor.csproj"}, "D:\\Projects\\VRTesting\\Oculus.VR.Scripts.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Oculus.VR.Scripts.Editor.csproj"}, "D:\\Projects\\VRTesting\\PPv2URPConverters.csproj": {"projectPath": "D:\\Projects\\VRTesting\\PPv2URPConverters.csproj"}, "D:\\Projects\\VRTesting\\RootMotion.csproj": {"projectPath": "D:\\Projects\\VRTesting\\RootMotion.csproj"}, "D:\\Projects\\VRTesting\\RootMotionEditor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\RootMotionEditor.csproj"}, "D:\\Projects\\VRTesting\\Unity.AI.Navigation.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.AI.Navigation.csproj"}, "D:\\Projects\\VRTesting\\Unity.AI.Navigation.Editor.ConversionSystem.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.AI.Navigation.Editor.ConversionSystem.csproj"}, "D:\\Projects\\VRTesting\\Unity.AI.Navigation.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.AI.Navigation.Editor.csproj"}, "D:\\Projects\\VRTesting\\Unity.AI.Navigation.Updater.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.AI.Navigation.Updater.csproj"}, "D:\\Projects\\VRTesting\\Unity.Burst.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.Burst.csproj"}, "D:\\Projects\\VRTesting\\Unity.Burst.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.Burst.Editor.csproj"}, "D:\\Projects\\VRTesting\\Unity.Collections.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.Collections.csproj"}, "D:\\Projects\\VRTesting\\Unity.Collections.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.Collections.Editor.csproj"}, "D:\\Projects\\VRTesting\\Unity.InputSystem.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.InputSystem.csproj"}, "D:\\Projects\\VRTesting\\Unity.InputSystem.ForUI.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.InputSystem.ForUI.csproj"}, "D:\\Projects\\VRTesting\\Unity.Mathematics.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.Mathematics.csproj"}, "D:\\Projects\\VRTesting\\Unity.Mathematics.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.Mathematics.Editor.csproj"}, "D:\\Projects\\VRTesting\\Unity.Multiplayer.Center.Common.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.Multiplayer.Center.Common.csproj"}, "D:\\Projects\\VRTesting\\Unity.Multiplayer.Center.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.Multiplayer.Center.Editor.csproj"}, "D:\\Projects\\VRTesting\\Unity.PlasticSCM.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.PlasticSCM.Editor.csproj"}, "D:\\Projects\\VRTesting\\Unity.Rendering.LightTransport.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.Rendering.LightTransport.Editor.csproj"}, "D:\\Projects\\VRTesting\\Unity.Rendering.LightTransport.Runtime.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.Rendering.LightTransport.Runtime.csproj"}, "D:\\Projects\\VRTesting\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj"}, "D:\\Projects\\VRTesting\\Unity.RenderPipelines.Core.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.RenderPipelines.Core.Editor.csproj"}, "D:\\Projects\\VRTesting\\Unity.RenderPipelines.Core.Editor.Shared.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.RenderPipelines.Core.Editor.Shared.csproj"}, "D:\\Projects\\VRTesting\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.RenderPipelines.Core.Runtime.csproj"}, "D:\\Projects\\VRTesting\\Unity.RenderPipelines.Core.Runtime.Shared.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.RenderPipelines.Core.Runtime.Shared.csproj"}, "D:\\Projects\\VRTesting\\Unity.RenderPipelines.Core.ShaderLibrary.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.RenderPipelines.Core.ShaderLibrary.csproj"}, "D:\\Projects\\VRTesting\\Unity.RenderPipelines.GPUDriven.Runtime.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.RenderPipelines.GPUDriven.Runtime.csproj"}, "D:\\Projects\\VRTesting\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj"}, "D:\\Projects\\VRTesting\\Unity.RenderPipelines.Universal.2D.Runtime.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.RenderPipelines.Universal.2D.Runtime.csproj"}, "D:\\Projects\\VRTesting\\Unity.RenderPipelines.Universal.Config.Runtime.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.RenderPipelines.Universal.Config.Runtime.csproj"}, "D:\\Projects\\VRTesting\\Unity.RenderPipelines.Universal.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.RenderPipelines.Universal.Editor.csproj"}, "D:\\Projects\\VRTesting\\Unity.RenderPipelines.Universal.Runtime.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.RenderPipelines.Universal.Runtime.csproj"}, "D:\\Projects\\VRTesting\\Unity.RenderPipelines.Universal.Shaders.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.RenderPipelines.Universal.Shaders.csproj"}, "D:\\Projects\\VRTesting\\Unity.Rider.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.Rider.Editor.csproj"}, "D:\\Projects\\VRTesting\\Unity.Searcher.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.Searcher.Editor.csproj"}, "D:\\Projects\\VRTesting\\Unity.ShaderGraph.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.ShaderGraph.Editor.csproj"}, "D:\\Projects\\VRTesting\\Unity.TextMeshPro.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.TextMeshPro.csproj"}, "D:\\Projects\\VRTesting\\Unity.TextMeshPro.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.TextMeshPro.Editor.csproj"}, "D:\\Projects\\VRTesting\\Unity.Timeline.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.Timeline.csproj"}, "D:\\Projects\\VRTesting\\Unity.Timeline.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.Timeline.Editor.csproj"}, "D:\\Projects\\VRTesting\\Unity.VisualScripting.Core.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.VisualScripting.Core.csproj"}, "D:\\Projects\\VRTesting\\Unity.VisualScripting.Core.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.VisualScripting.Core.Editor.csproj"}, "D:\\Projects\\VRTesting\\Unity.VisualScripting.Flow.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.VisualScripting.Flow.csproj"}, "D:\\Projects\\VRTesting\\Unity.VisualScripting.Flow.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.VisualScripting.Flow.Editor.csproj"}, "D:\\Projects\\VRTesting\\Unity.VisualScripting.SettingsProvider.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.VisualScripting.SettingsProvider.Editor.csproj"}, "D:\\Projects\\VRTesting\\Unity.VisualScripting.Shared.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.VisualScripting.Shared.Editor.csproj"}, "D:\\Projects\\VRTesting\\Unity.VisualScripting.State.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.VisualScripting.State.csproj"}, "D:\\Projects\\VRTesting\\Unity.VisualScripting.State.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.VisualScripting.State.Editor.csproj"}, "D:\\Projects\\VRTesting\\Unity.VisualStudio.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.VisualStudio.Editor.csproj"}, "D:\\Projects\\VRTesting\\Unity.XR.CoreUtils.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.XR.CoreUtils.csproj"}, "D:\\Projects\\VRTesting\\Unity.XR.CoreUtils.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.XR.CoreUtils.Editor.csproj"}, "D:\\Projects\\VRTesting\\Unity.XR.Management.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.XR.Management.csproj"}, "D:\\Projects\\VRTesting\\Unity.XR.Management.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.XR.Management.Editor.csproj"}, "D:\\Projects\\VRTesting\\Unity.XR.Oculus.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.XR.Oculus.csproj"}, "D:\\Projects\\VRTesting\\Unity.XR.Oculus.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.XR.Oculus.Editor.csproj"}, "D:\\Projects\\VRTesting\\Unity.XR.OpenXR.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.XR.OpenXR.csproj"}, "D:\\Projects\\VRTesting\\Unity.XR.OpenXR.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.XR.OpenXR.Editor.csproj"}, "D:\\Projects\\VRTesting\\Unity.XR.OpenXR.Features.ConformanceAutomation.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.XR.OpenXR.Features.ConformanceAutomation.csproj"}, "D:\\Projects\\VRTesting\\Unity.XR.OpenXR.Features.MetaQuestSupport.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.XR.OpenXR.Features.MetaQuestSupport.csproj"}, "D:\\Projects\\VRTesting\\Unity.XR.OpenXR.Features.MetaQuestSupport.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.XR.OpenXR.Features.MetaQuestSupport.Editor.csproj"}, "D:\\Projects\\VRTesting\\Unity.XR.OpenXR.Features.OculusQuestSupport.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.XR.OpenXR.Features.OculusQuestSupport.csproj"}, "D:\\Projects\\VRTesting\\Unity.XR.OpenXR.Features.OculusQuestSupport.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.XR.OpenXR.Features.OculusQuestSupport.Editor.csproj"}, "D:\\Projects\\VRTesting\\Unity.XR.OpenXR.Features.RuntimeDebugger.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\Unity.XR.OpenXR.Features.RuntimeDebugger.Editor.csproj"}, "D:\\Projects\\VRTesting\\UnityEditor.SpatialTracking.csproj": {"projectPath": "D:\\Projects\\VRTesting\\UnityEditor.SpatialTracking.csproj"}, "D:\\Projects\\VRTesting\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Projects\\VRTesting\\UnityEditor.TestRunner.csproj"}, "D:\\Projects\\VRTesting\\UnityEditor.UI.csproj": {"projectPath": "D:\\Projects\\VRTesting\\UnityEditor.UI.csproj"}, "D:\\Projects\\VRTesting\\UnityEditor.XR.LegacyInputHelpers.csproj": {"projectPath": "D:\\Projects\\VRTesting\\UnityEditor.XR.LegacyInputHelpers.csproj"}, "D:\\Projects\\VRTesting\\UnityEngine.SpatialTracking.csproj": {"projectPath": "D:\\Projects\\VRTesting\\UnityEngine.SpatialTracking.csproj"}, "D:\\Projects\\VRTesting\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Projects\\VRTesting\\UnityEngine.TestRunner.csproj"}, "D:\\Projects\\VRTesting\\UnityEngine.UI.csproj": {"projectPath": "D:\\Projects\\VRTesting\\UnityEngine.UI.csproj"}, "D:\\Projects\\VRTesting\\UnityEngine.XR.LegacyInputHelpers.csproj": {"projectPath": "D:\\Projects\\VRTesting\\UnityEngine.XR.LegacyInputHelpers.csproj"}, "D:\\Projects\\VRTesting\\VoiceSDK.Dictation.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\VoiceSDK.Dictation.Editor.csproj"}, "D:\\Projects\\VRTesting\\VoiceSDK.Dictation.Runtime.csproj": {"projectPath": "D:\\Projects\\VRTesting\\VoiceSDK.Dictation.Runtime.csproj"}, "D:\\Projects\\VRTesting\\VoiceSDK.Editor.Composer.csproj": {"projectPath": "D:\\Projects\\VRTesting\\VoiceSDK.Editor.Composer.csproj"}, "D:\\Projects\\VRTesting\\VoiceSDK.Editor.csproj": {"projectPath": "D:\\Projects\\VRTesting\\VoiceSDK.Editor.csproj"}, "D:\\Projects\\VRTesting\\VoiceSDK.Runtime.Composer.csproj": {"projectPath": "D:\\Projects\\VRTesting\\VoiceSDK.Runtime.Composer.csproj"}, "D:\\Projects\\VRTesting\\VoiceSDK.Runtime.csproj": {"projectPath": "D:\\Projects\\VRTesting\\VoiceSDK.Runtime.csproj"}, "D:\\Projects\\VRTesting\\VoiceSDK.Telemetry.csproj": {"projectPath": "D:\\Projects\\VRTesting\\VoiceSDK.Telemetry.csproj"}, "D:\\Projects\\VRTesting\\VoiceSDK.Telemetry.Runtime.csproj": {"projectPath": "D:\\Projects\\VRTesting\\VoiceSDK.Telemetry.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}}}}