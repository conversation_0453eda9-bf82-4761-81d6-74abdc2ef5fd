﻿<Project>
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp\obj\$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <UseCommonOutputDirectory>true</UseCommonOutputDirectory>
    <OutputPath>Temp\bin\Debug\</OutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <LangVersion>9.0</LangVersion>
    <RootNamespace></RootNamespace>
    <OutputType>Library</OutputType>
    <AssemblyName>Unity.RenderPipelines.Core.Runtime</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup>
    <NoWarn>0169;USG0001</NoWarn>
    <DefineConstants>UNITY_6000_1_12;UNITY_6000_1;UNITY_6000;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;UNITY_2023_1_OR_NEWER;UNITY_2023_2_OR_NEWER;UNITY_2023_3_OR_NEWER;UNITY_6000_0_OR_NEWER;UNITY_6000_1_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_VIRTUALTEXTURING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_MARSHALLING_TESTS;ENABLE_VIDEO;ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;TEXTCORE_1_0_OR_NEWER;EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED;PLATFORM_STANDALONE_WIN;PLATFORM_STANDALONE;UNITY_STANDALONE_WIN;UNITY_STANDALONE;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_NVIDIA;ENABLE_AMD;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_OUT_OF_PROCESS_CRASH_HANDLER;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;GFXDEVICE_WAITFOREVENT_MESSAGEPUMP;PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER;PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION;PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS;ENABLE_MONO;NET_STANDARD_2_0;NET_STANDARD;NET_STANDARD_2_1;NETSTANDARD;NETSTANDARD2_1;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER;USE_INPUT_SYSTEM_POSE_CONTROL;USE_STICK_CONTROL_THUMBSTICKS;ISDK_OPENXR_HAND;ENABLE_VR_MODULE;ENABLE_XR_MODULE;ENABLE_INPUT_SYSTEM_PACKAGE;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.23</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Game:1</UnityProjectType>
    <UnityBuildTarget>StandaloneWindows64:19</UnityBuildTarget>
    <UnityVersion>6000.1.12f1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="C:\Users\<USER>\.vscode\extensions\visualstudiotoolsforunity.vstuc-1.1.2\Analyzers\Microsoft.Unity.Analyzers.dll" />
    <Analyzer Include="C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.SourceGenerators.dll" />
    <Analyzer Include="C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.Properties.SourceGenerator.dll" />
    <Analyzer Include="C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.UIToolkit.SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Volume\VolumeComponent.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lights\LightAnchor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\RenderGraphResources.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\IRenderGraphRecorder.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\Compiler\FixedAttachmentArray.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerValueTuple.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Vrs\VrsShaders.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\TextureCurve.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\RenderGraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Stripping\RenderPipelineGraphicsSettingsStripper.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerUIntField.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\CommandBuffers\IBaseCommandBuffer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Textures\RTHandle.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\DebugUI.Containers.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\SceneRenderPipeline.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Common\DynamicArray.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\RenderGraphGlobalSettings.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\GPUPrefixSum\GPUPrefixSum.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\CommandBuffers\IRasterCommandBuffer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Common\IAdditionalData.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeVolumeBakingProcessSettings.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\FSRUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\UIFoldout.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeReferenceVolume.Binding.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerContainer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeReferenceVolume.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ShaderVariablesProbeVolumes.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Documentation.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerIndirectToggle.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\ArrayExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\CommandBuffers\UnsafeCommandBuffer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Vrs\VrsRenderPipelineRuntimeResources.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Volume\IVolume.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeVolumeGIContributor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeVolumeConstantRuntimeResources.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\RenderGraphCompilationCache.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\Compiler\NativePassCompiler.Debug.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeBrickPool.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Settings\LightmapSamplingSettings.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeVolumeBakingSet.Editor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\ColorUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerObjectPopupField.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Common\CommandBufferPool.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\DebugUI.Fields.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderPipeline\ICloudBackground.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Volume\KeyframeUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerRenderingLayerField.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeVolumePositioning.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Common\ReloadGroupAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Textures\MSAASamples.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\IProbeVolumeEnabledRenderPipeline.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\RenderGraphUtilsResources.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Textures\PowerOfTwoTextureAtlas.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Common\ObjectPools.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Textures\DepthBits.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Textures\TextureXR.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\DebugOverlay.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\RenderGraphPassType.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\DebugDisplaySettings.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\XR\XRLayoutStack.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Common\ObservableList.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\DebugUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\CommandBuffers\CommandBufferHelpers.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\PostProcessing\LensFlareDataSRP.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\XR\XRLayout.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeReferenceVolume.Debug.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\XR\XRSystem.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\DebugDisplaySettingsHDROutput.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Common\ContextContainer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\IDebugDisplaySettingsPanel.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerVector2.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\MaterialQuality.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Settings\IncludeRenderPipelineAsset.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\FrameTiming\FrameTimeSample.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeVolumeStreamableAsset.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Settings\IDefaultVolumeProfileSettings.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\XR\XRGraphicsAutomatedTests.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Volume\VolumeComponent.EditorOnly.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\CommandBuffers\IComputeCommandBuffer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Common\Observable.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Common\IVirtualTexturingEnabledRenderPipeline.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\ShaderGenerator\ShaderGeneratorAttributes.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Common\DynamicString.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\Debug\DebugDisplaySettingsRenderGraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\RenderGraphResourceAccelerationStructure.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\HableCurve.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerToggle.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\RenderGraphDefaultResources.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\DebugDisplaySettingsVolumes.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerToggleHistory.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\HaltonSequence.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\Compiler\ResourcesData.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\RenderGraphResourceTexture.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\CoreRenderPipelinePreferences.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Volume\VolumeParameter.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\CommandBuffers\RasterCommandBuffer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\CommandBuffers\ComputeCommandBuffer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\DebugDisplaySettingsPanel.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Vrs\VrsResources.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\Compiler\CompilerContextData.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Settings\ShaderStrippingSetting.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\Compiler\NativePassCompiler.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\HDROutputUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\XR\XRMirrorView.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\PostProcessing\LensFlareCommonSRP.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\RenderGraphUtilsBlit.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Common\ComponentSingleton.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Common\RemoveRange.Extensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Stripping\RenderPipelineGraphicsSettingsStripperFetcher.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\DebugUI.Panel.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\CullContextData.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\XR\XRBuiltinShaderConstants.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderPipeline\RenderPipelineGraphicsSettingsContainer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeAdjustmentVolume.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\RenderGraphLogger.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Vrs\VrsLut.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\XR\XRPass.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\GPUPrefixSum\GPUPrefixSum.ShaderIDs.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Volume\VolumeManager.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Common\CoreUnsafeUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\XR\XRSRPSettings.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerCanvas.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeVolumePerSceneData.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\GPUSort\GPUSort.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Common\CoreProfileId.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Stripping\RenderPipelineGraphicsSettingsStripperReport.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeIndexOfIndices.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\RenderGraph.DebugData.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\IDebugDisplaySettingsData.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\XR\XRView.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\IRenderGraphEnabledRenderPipeline.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Common\ListBuffer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Stripping\IStripper.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerGroup.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeVolumeAsset.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\BitArray.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Common\DynamicResolutionHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderPipeline\RenderPipelineGlobalSettings.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\PostProcessing\LensFlareOcclusionPermutation.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Camera\CameraHistory.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\DebugDisplaySettingsStats.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\LookDev\IDataProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Stripping\IRenderPipelineGraphicsSettingsStripper.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\DebugUpdater.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\IDebugDisplaySettingsQuery.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\ShaderDebugPrintManager.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderPipeline\RenderPipelineGlobalSettingsUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Inputs\InputRegistering.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerWidget.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\DebugManager.Actions.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Common\ReloadAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerEnumHistory.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Volume\VolumeProfile.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\TextureGradient.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\RenderGraph.Compiler.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeVolume.Migration.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerVector4.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\MousePositionDebug.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerProgressBar.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerFloatField.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\GPUPrefixSum\GPUPrefixSum.Data.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\CommandBuffers\BaseCommandBufer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\RenderGraphResourceRendererList.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\MeshGizmo.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerValue.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\RenderGraphPass.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderPipeline\IVolumetricCloud.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\PostProcessing\IPostProcessComponent.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerFoldout.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Volume\VolumeDebugData.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeVolume.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerMessageBox.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\ColorSpaceUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\RenderGraphResourceRegistry.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\SphericalHarmonics.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\CoreMatrixUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\CommandBuffers\IUnsafeCommandBuffer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\RenderGraphBuilder.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeVolumeSceneData.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\VolumeDebugSettings.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Volume\VolumeCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeVolumeScratchBufferPool.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\DebugManager.UIState.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\ShaderLibrary\Sampling\Hammersley.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Camera\FreeCamera.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\ProfilingScope.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\DebugManager.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\HashFNV1A32.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeBrickIndex.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Settings\IDefaultVolumeProfileResource.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\Blitter.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\CameraCaptureBridge.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\DebugDisplaySettingsUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\STP\ISTPEnabledRenderPipeline.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Common\Swap.Extensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\RenderGraphResourceBuffer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\AssemblyInfo.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\XR\XROcclusionMesh.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\Debug\RenderGraphDebugParams.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerObject.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\IDebugDisplaySettings.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Common\CommonStructs.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\DelegateUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\RenderGraphResourcePool.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeVolumeBakingSet.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\GPUSort\GPUSort.Data.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\RenderGraphObjectPool.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeReferenceVolume.Streaming.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Textures\BufferedRTHandleSystem.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Textures\RTHandleSystem.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\IRenderGraphBuilder.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Common\ConstantBuffer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Textures\RTHandles.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerField.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\IVolumeDebugSettings.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerPanel.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Common\GlobalDynamicResolutionSettings.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerIndirectFloatField.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerRow.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\STP\STP.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerEnumField.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Volume\Volume.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerObjectList.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Common\SerializedDictionary.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Volume\VolumeStack.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerHBox.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeVolumesOptions.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeReferenceVolume.ReflProbeNormalization.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerIntField.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Vrs\Vrs.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\RenderGraphBuilders.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerVBox.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\PostProcessing\HDROutputDefines.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\TileLayoutUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\CoreUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\DebugShapes.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\Compiler\PassesData.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Camera\CameraSwitcher.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\DebugFrameTiming.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderPipeline\RenderPipelineResources.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Common\SerializableEnum.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderGraph\RenderGraphProfileId.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerColor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerPersistentCanvas.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\ResourceReloader.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\FrameTiming\FrameTimeBottleneck.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerButton.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Textures\Texture2DAtlas.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeVolumeGlobalSettings.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Common\CoreAttributes.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\DebugDisplayStats.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\BatchRendererGroupGlobals.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerVector3.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\PostProcessing\LensFlareComponentSRP.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerBitField.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\LightUnitUtils.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\ShaderLibrary\Sampling\Hammersley.cs.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\STP\StpCommon.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\PostProcessing\HDROutputDefines.cs.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeVolumeUploadDataL2.compute" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\PostProcessing\LensFlareOcclusionPermutation.cs.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Vrs\Shaders\VrsVisualization.shader" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\STP\STP.cs.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\PostProcessing\Shaders\LensFlareCommon.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderPipelineResources\GPUDriven\OcclusionCullingDebug.compute" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Vrs\Shaders\VrsTileSize.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\STP\StpSetup.compute" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderPipelineResources\GPUDriven\InstanceDataBufferUploadKernels.compute" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeVolumeBlendStates.compute" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderPipelineResources\GPUDriven\DebugOccluder.shader" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderPipelineResources\GPUDriven\DebugOcclusionTest.shader" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\PostProcessing\Shaders\ffx\ffx_a.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeVolumeBlendStates.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\Blit.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeReferenceVolume.Streaming.cs.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderPipelineResources\FallbackShader.shader" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\GPUPrefixSum\GPUPrefixSum.Data.cs.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\DecodeSH.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\STP\StpPreTaa.compute" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeReferenceVolume.Debug.cs.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderPipelineResources\GPUDriven\InstanceOcclusionCullingKernels.compute" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderPipelineResources\GPUDriven\OccluderDepthPyramidKernels.compute" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Vrs\Shaders\VrsMainTex.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeVolume.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeVolumeUploadData.compute" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\STP\StpTaa.compute" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderPipelineResources\GPUDriven\InstanceDataBufferCopyKernels.compute" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\PostProcessing\Shaders\ffx\ffx_fsr1.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderPipelineResources\GPUDriven\InstanceTransformUpdateKernels.compute" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\PostProcessing\Shaders\ffx\ffx_cas.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debug\ProbeVolumeFragmentationDebug.shader" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Vrs\Shaders\VrsTexture.compute" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debug\ProbeVolumeDebug.shader" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debug\ProbeVolumeDebugFunctions.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\PostProcessing\Shaders\ffx\ffx_lpm.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Vrs\Shaders\VrsShadingRates.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debug\ProbeVolumeOffsetDebug.shader" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ProbeVolumeUploadDataCommon.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debug\ProbeVolumeDebugBase.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Vrs\Shaders\VrsImage.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Lighting\ProbeVolume\ShaderVariablesProbeVolumes.cs.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Utilities\BlitColorAndDepth.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\STP\Stp.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\RenderPipelineResources\GPUDriven\InstanceWindDataUpdateKernels.compute" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\PostProcessing\LensFlareDataSRP.cs.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\PostProcessing\Shaders\FSRCommon.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\PostProcessing\Shaders\LensFlareScreenSpaceCommon.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Debug\ProbeVolumeSamplingDebug.shader" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@47a0d8baabbd\Runtime\Unity.RenderPipelines.Core.Runtime.asmdef" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.ContentLoadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GraphicsStateCollectionSerializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HierarchyCoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.HierarchyCoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputForUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputForUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MarshallingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.MarshallingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MultiplayerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ShaderVariantAnalyticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.ShaderVariantAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.VirtualTexturingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AccessibilityModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AdaptivePerformanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.AdaptivePerformanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.BuildProfileModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.BuildProfileModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreBusinessMetricsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreBusinessMetricsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.EditorToolbarModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EmbreeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.EmbreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphicsStateCollectionSerializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridAndSnapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridAndSnapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.MultiplayerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Physics2DModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.PresetsUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PropertiesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SafeModeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.SafeModeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.ShaderFoundryModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.ShaderFoundryModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SketchUpModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.SketchUpModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteMaskModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteShapeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SubstanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TerrainModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextRenderingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TilemapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TreeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.TreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIAutomationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIAutomationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UmbraModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VFXModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VideoModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.XRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Graphs">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEditor.Graphs.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.WebGL.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\WebGLSupport\UnityEditor.WebGL.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.OSXStandalone.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\MacStandaloneSupport\UnityEditor.OSXStandalone.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VisionOS.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\VisionOSPlayer\UnityEditor.VisionOS.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.WindowsStandalone.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\WindowsStandaloneSupport\UnityEditor.WindowsStandalone.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UWP.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\MetroSupport\UnityEditor.UWP.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Android.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\AndroidPlayer\UnityEditor.Android.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AppleTV.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\AppleTVSupport\UnityEditor.AppleTV.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.LinuxStandalone.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\LinuxStandaloneSupport\UnityEditor.LinuxStandalone.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.YamlDotNet">
      <HintPath>Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>Library\PackageCache\com.unity.collections@56bff8827a7e\Unity.Collections.LowLevel.ILSupport\Unity.Collections.LowLevel.ILSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>Library\PackageCache\com.unity.ext.nunit@031a54704bff\net40\unity-custom\nunit.framework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="unityplastic">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\unityplastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Antlr3.Runtime">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Unity.Plastic.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Newtonsoft.Json">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Unity.Plastic.Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="log4netPlastic">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\log4netPlastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.IonicZip">
      <HintPath>Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Antlr3.Runtime">
      <HintPath>Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Flow\Dependencies\NCalc\Unity.VisualScripting.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>Library\PackageCache\com.unity.nuget.newtonsoft-json@74deb55db2a0\Runtime\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.TextureAssets">
      <HintPath>Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Editor\VisualScripting.Core\EditorAssetResources\Unity.VisualScripting.TextureAssets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>Library\PackageCache\com.unity.nuget.mono-cecil@d6f9955a5d5f\Mono.Cecil.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DemiEditor">
      <HintPath>Assets\Plugins\Demigiant\DemiLib\Core\Editor\DemiEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>Assets\Plugins\Demigiant\DOTween\DOTween.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenUpgradeManager">
      <HintPath>Assets\Plugins\Demigiant\DOTween\Editor\DOTweenUpgradeManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenPro">
      <HintPath>Assets\Plugins\Demigiant\DOTweenPro\DOTweenPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DemiLib">
      <HintPath>Assets\Plugins\Demigiant\DemiLib\Core\DemiLib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenProEditor">
      <HintPath>Assets\Plugins\Demigiant\DOTweenPro\Editor\DOTweenProEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenEditor">
      <HintPath>Assets\Plugins\Demigiant\DOTween\Editor\DOTweenEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\AppleTVSupport\UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Apple.Extensions.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\AppleTVSupport\UnityEditor.Apple.Extensions.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Apple.Extensions.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.Apple.Extensions.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Types">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Types.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Gradle">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Gradle.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\VisionOSPlayer\UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Apple.Extensions.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\VisionOSPlayer\UnityEditor.Apple.Extensions.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\MacStandaloneSupport\UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\ref\2.1.0\netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Buffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tracing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.DispatchProxy.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\Extensions\2.0.0\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Web">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ServiceModel.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Web">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Windows">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Windows.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Serialization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Unity.InputSystem.csproj" />
    <ProjectReference Include="Unity.Mathematics.csproj" />
    <ProjectReference Include="Unity.Collections.csproj" />
    <ProjectReference Include="Unity.Burst.csproj" />
    <ProjectReference Include="UnityEditor.UI.csproj" />
    <ProjectReference Include="UnityEngine.UI.csproj" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>
