using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Events;
using TMPro;
using System.Collections;
using DG.Tweening;

public class CalibrationManager : MonoBehaviour
{
    [Header("UI Elements")]
    [Tooltip("The main title text - 'Ready to Calibrate'")]
    public TextMeshProUGUI titleText;

    [Tooltip("The calibrate button")]
    public Button calibrateButton;

    [<PERSON>lt<PERSON>("The close button")]
    public Button closeButton;

    [<PERSON>lt<PERSON>("The status indicator circle")]
    public Image statusIndicator;

    [Tooltip("The completion message text - 'Calibration Complete!'")]
    public TextMeshProUGUI completionText;

    [Tooltip("Main calibration panel (for animations)")]
    public RectTransform calibrationPanel;

    [Header("Color Settings")]
    [Tooltip("Color for the status indicator when ready (gray)")]
    public Color readyColor = new Color(0.5f, 0.5f, 0.5f, 1f); // Gray

    [Tooltip("Color for the status indicator when calibrating (yellow)")]
    public Color calibratingColor = new Color(1f, 1f, 0f, 1f); // Yellow

    [Tooltip("Color for the status indicator when complete (green)")]
    public Color completeColor = new Color(0f, 1f, 0f, 1f); // Green

    [Tooltip("Color for completion text (green)")]
    public Color completionTextColor = new Color(0f, 1f, 0f, 1f); // Green

    [Header("Calibration Settings")]
    [Tooltip("Duration of calibration process in seconds")]
    public float calibrationDuration = 3f;

    [Tooltip("Reference to VRIK calibration component (optional)")]
    public MonoBehaviour vrikCalibration;

    [Tooltip("Reference to MetaAutoHandReadyNotifier to check AutoHand status")]
    public MetaAutoHandReadyNotifier autoHandNotifier;

    [Header("Calibration Events")]
    [Tooltip("Event called when calibration starts - connect calibration methods here")]
    public UnityEvent OnCalibrationStart;

    [Tooltip("Event called when calibration completes")]
    public UnityEvent OnCalibrationComplete;

    [Header("Status Colors")]
    [Tooltip("Color when waiting for AutoHand (red)")]
    public Color waitingAutoHandColor = Color.red;

    [Header("Animation Settings")]
    [Tooltip("Duration for UI animations")]
    public float animationDuration = 0.5f;

    [Tooltip("Animation ease type")]
    public Ease animationEase = Ease.OutBack;

    [Tooltip("Scale animation for status indicator")]
    public float statusIndicatorPulseScale = 1.2f;

    [Tooltip("Fade animation duration")]
    public float fadeAnimationDuration = 0.3f;

    // Private variables
    private bool isCalibrating = false;
    private bool isCalibrated = false;
    private bool autoHandReady = false;
    private Vector3 originalPanelScale;
    private Vector3 originalStatusScale;

    void Start()
    {
        InitializeUI();

        // Subscribe to AutoHand ready event if notifier is assigned
        if (autoHandNotifier != null)
        {
            autoHandNotifier.OnAutoHandReady.AddListener(OnAutoHandReady);
        }
    }

    void InitializeUI()
    {
        // Store original scales for animations
        if (calibrationPanel != null)
            originalPanelScale = calibrationPanel.localScale;

        if (statusIndicator != null)
            originalStatusScale = statusIndicator.transform.localScale;

        // Check AutoHand status and set initial UI state
        bool autoHandSystemReady = CheckAutoHandStatus();

        if (autoHandSystemReady)
        {
            if (titleText != null)
                titleText.text = "Ready to Calibrate";

            if (statusIndicator != null)
                statusIndicator.color = readyColor;
        }
        else
        {
            if (titleText != null)
                titleText.text = "Waiting for AutoHand...";

            if (statusIndicator != null)
                statusIndicator.color = waitingAutoHandColor;
        }

        if (completionText != null)
        {
            completionText.text = "Calibration Complete!";
            completionText.color = completionTextColor;
            completionText.gameObject.SetActive(false);
        }

        // Setup calibrate button
        if (calibrateButton != null)
        {
            calibrateButton.onClick.AddListener(StartCalibration);
            calibrateButton.interactable = autoHandSystemReady;

            UpdateCalibrateButtonText();
        }

        // Setup close button
        if (closeButton != null)
        {
            closeButton.onClick.AddListener(CloseCalibrationPanel);

            // Set close button text if it has a TextMeshProUGUI component
            TextMeshProUGUI closeButtonText = closeButton.GetComponentInChildren<TextMeshProUGUI>();
            if (closeButtonText != null)
                closeButtonText.text = "×"; // Close symbol
        }

        // Animate panel entrance
        AnimatePanelEntrance();
    }

    bool CheckAutoHandStatus()
    {
        if (autoHandNotifier != null)
        {
            // Check if the notifier component is still enabled (it disables itself when ready)
            return !autoHandNotifier.enabled;
        }

        // If no notifier assigned, assume AutoHand is ready
        return true;
    }

    void OnAutoHandReady()
    {
        autoHandReady = true;

        if (titleText != null)
            titleText.text = "Ready to Calibrate";

        if (statusIndicator != null)
            statusIndicator.color = readyColor;

        if (calibrateButton != null)
        {
            calibrateButton.interactable = true;
            UpdateCalibrateButtonText();
        }
    }

    void UpdateCalibrateButtonText()
    {
        if (calibrateButton != null)
        {
            TextMeshProUGUI buttonText = calibrateButton.GetComponentInChildren<TextMeshProUGUI>();
            if (buttonText != null)
            {
                if (isCalibrated)
                    buttonText.text = "Recalibrate";
                else if (isCalibrating)
                    buttonText.text = "Calibrating...";
                else
                    buttonText.text = "Calibrate";
            }
        }
    }

    public void StartCalibration()
    {
        // If already calibrated, reset first to allow re-calibration
        if (isCalibrated)
        {
            ResetCalibration();
            return;
        }

        // Don't start if already calibrating
        if (isCalibrating)
            return;

        // Check if AutoHand is ready before starting calibration
        if (!CheckAutoHandStatus())
        {
            Debug.LogWarning("Cannot start calibration: AutoHand is not ready yet!");
            return;
        }

        StartCoroutine(CalibrationProcess());
    }

    private IEnumerator CalibrationProcess()
    {
        isCalibrating = true;

        // Update UI for calibrating state with animations
        if (titleText != null)
        {
            titleText.text = "Calibrating...";
            titleText.transform.DOPunchScale(Vector3.one * 0.1f, 0.3f, 5, 0.5f);
        }

        // Animate status indicator color change
        AnimateStatusIndicator(calibratingColor);

        if (calibrateButton != null)
        {
            calibrateButton.interactable = false;
            UpdateCalibrateButtonText();
            // Animate button scale down
            calibrateButton.transform.DOScale(0.9f, 0.2f).SetEase(Ease.OutQuad);
        }

        // Invoke calibration start event
        OnCalibrationStart?.Invoke();

        // Perform actual calibration
        yield return StartCoroutine(PerformCalibration());

        // Update UI for completed state with animations
        if (titleText != null)
        {
            titleText.text = "Calibration Complete";
            titleText.transform.DOPunchScale(Vector3.one * 0.15f, 0.5f, 8, 0.8f);
        }

        // Animate status indicator to complete color
        AnimateStatusIndicator(completeColor);

        // Animate completion text appearance
        AnimateCompletionText(true);

        isCalibrating = false;
        isCalibrated = true;

        // Update button text to show "Recalibrate"
        UpdateCalibrateButtonText();

        // Invoke calibration complete event
        OnCalibrationComplete?.Invoke();

        Debug.Log("Calibration Complete!");
    }

    private IEnumerator PerformCalibration()
    {
        // If VRIK calibration is assigned, call its calibrate method
        if (vrikCalibration != null)
        {
            vrikCalibration
            // Try to call Calibrate method if it exists
            var calibrateMethod = vrikCalibration.GetType().GetMethod("Calibrate");
            if (calibrateMethod != null)
            {
                calibrateMethod.Invoke(vrikCalibration, null);
            }
        }

        // Wait for calibration duration
        yield return new WaitForSeconds(calibrationDuration);
    }

    public void ResetCalibration()
    {
        isCalibrated = false;
        isCalibrating = false;

        // Check AutoHand status before resetting UI
        bool autoHandSystemReady = CheckAutoHandStatus();

        if (titleText != null)
        {
            titleText.text = autoHandSystemReady ? "Ready to Calibrate" : "Waiting for AutoHand...";
            titleText.transform.DOPunchScale(Vector3.one * 0.1f, 0.3f, 5, 0.5f);
        }

        // Animate status indicator color change
        Color targetColor = autoHandSystemReady ? readyColor : waitingAutoHandColor;
        AnimateStatusIndicator(targetColor);

        // Hide completion text with animation
        AnimateCompletionText(false);

        if (calibrateButton != null)
        {
            calibrateButton.interactable = autoHandSystemReady;
            UpdateCalibrateButtonText();
            // Animate button back to normal scale
            calibrateButton.transform.DOScale(Vector3.one, 0.2f).SetEase(Ease.OutQuad);
        }
    }

    #region Animation Methods

    void AnimatePanelEntrance()
    {
        if (calibrationPanel != null)
        {
            // Start from scale 0 and animate to original scale
            calibrationPanel.localScale = Vector3.zero;
            calibrationPanel.DOScale(originalPanelScale, animationDuration)
                .SetEase(animationEase);
        }

        // Fade in completion text if it exists
        if (completionText != null)
        {
            var canvasGroup = completionText.GetComponent<CanvasGroup>();
            if (canvasGroup == null)
                canvasGroup = completionText.gameObject.AddComponent<CanvasGroup>();

            canvasGroup.alpha = 0f;
        }
    }

    void AnimateStatusIndicator(Color targetColor)
    {
        if (statusIndicator != null)
        {
            // Color transition
            statusIndicator.DOColor(targetColor, fadeAnimationDuration);

            // Pulse animation
            statusIndicator.transform.DOScale(originalStatusScale * statusIndicatorPulseScale, 0.2f)
                .SetEase(Ease.OutQuad)
                .OnComplete(() => {
                    statusIndicator.transform.DOScale(originalStatusScale, 0.2f)
                        .SetEase(Ease.InQuad);
                });
        }
    }

    void AnimateCompletionText(bool show)
    {
        if (completionText != null)
        {
            var canvasGroup = completionText.GetComponent<CanvasGroup>();
            if (canvasGroup == null)
                canvasGroup = completionText.gameObject.AddComponent<CanvasGroup>();

            if (show)
            {
                completionText.gameObject.SetActive(true);
                canvasGroup.DOFade(1f, fadeAnimationDuration)
                    .SetEase(Ease.OutQuad);

                // Scale animation for completion text
                completionText.transform.localScale = Vector3.zero;
                completionText.transform.DOScale(Vector3.one, animationDuration)
                    .SetEase(animationEase)
                    .SetDelay(0.1f);
            }
            else
            {
                canvasGroup.DOFade(0f, fadeAnimationDuration)
                    .SetEase(Ease.InQuad)
                    .OnComplete(() => completionText.gameObject.SetActive(false));
            }
        }
    }

    public void CloseCalibrationPanel()
    {
        // Reset calibration state when closing
        ResetCalibration();

        if (calibrationPanel != null)
        {
            calibrationPanel.DOScale(Vector3.zero, animationDuration)
                .SetEase(Ease.InBack)
                .OnComplete(() => {
                    gameObject.SetActive(false);
                });
        }
        else
        {
            // Fallback if no panel assigned
            gameObject.SetActive(false);
        }
    }

    #endregion

    void OnDestroy()
    {
        // Kill all DOTween animations on this object to prevent errors
        transform.DOKill();
        if (calibrationPanel != null)
            calibrationPanel.DOKill();
        if (statusIndicator != null)
            statusIndicator.transform.DOKill();
        if (titleText != null)
            titleText.transform.DOKill();
        if (calibrateButton != null)
            calibrateButton.transform.DOKill();
        if (completionText != null)
            completionText.transform.DOKill();
    }
}
