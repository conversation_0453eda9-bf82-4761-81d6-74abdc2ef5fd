using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;

public class CalibrationManager : MonoBehaviour
{
    [<PERSON><PERSON>("UI Elements")]
    [Tooltip("The main title text - 'Ready to Calibrate'")]
    public TextMeshProUGUI titleText;

    [Tooltip("The calibrate button")]
    public Button calibrateButton;

    [<PERSON>lt<PERSON>("The status indicator circle")]
    public Image statusIndicator;

    [<PERSON>lt<PERSON>("The completion message text - 'Calibration Complete!'")]
    public TextMeshProUGUI completionText;

    [<PERSON><PERSON>("Color Settings")]
    [Tooltip("Color for the status indicator when ready (gray)")]
    public Color readyColor = new Color(0.5f, 0.5f, 0.5f, 1f); // Gray

    [Tooltip("Color for the status indicator when calibrating (yellow)")]
    public Color calibratingColor = new Color(1f, 1f, 0f, 1f); // Yellow

    [Tooltip("Color for the status indicator when complete (green)")]
    public Color completeColor = new Color(0f, 1f, 0f, 1f); // Green

    [Tooltip("Color for completion text (green)")]
    public Color completionTextColor = new Color(0f, 1f, 0f, 1f); // Green

    [Header("Calibration Settings")]
    [Tooltip("Duration of calibration process in seconds")]
    public float calibrationDuration = 3f;

    [Tooltip("Reference to VRIK calibration component (optional)")]
    public MonoBehaviour vrikCalibration;

    // Private variables
    private bool isCalibrating = false;
    private bool isCalibrated = false;

    void Start()
    {
        InitializeUI();
    }

    void InitializeUI()
    {
        // Set initial UI state
        if (titleText != null)
            titleText.text = "Ready to Calibrate";

        if (statusIndicator != null)
            statusIndicator.color = readyColor;

        if (completionText != null)
        {
            completionText.text = "Calibration Complete!";
            completionText.color = completionTextColor;
            completionText.gameObject.SetActive(false);
        }

        // Setup button
        if (calibrateButton != null)
        {
            calibrateButton.onClick.AddListener(StartCalibration);

            // Set button text if it has a TextMeshProUGUI component
            TextMeshProUGUI buttonText = calibrateButton.GetComponentInChildren<TextMeshProUGUI>();
            if (buttonText != null)
                buttonText.text = "Calibrate";
        }
    }

    public void StartCalibration()
    {
        if (isCalibrating || isCalibrated)
            return;

        StartCoroutine(CalibrationProcess());
    }

    private IEnumerator CalibrationProcess()
    {
        isCalibrating = true;

        // Update UI for calibrating state
        if (titleText != null)
            titleText.text = "Calibrating...";

        if (statusIndicator != null)
            statusIndicator.color = calibratingColor;

        if (calibrateButton != null)
            calibrateButton.interactable = false;

        // Perform actual calibration
        yield return StartCoroutine(PerformCalibration());

        // Update UI for completed state
        if (titleText != null)
            titleText.text = "Calibration Complete";

        if (statusIndicator != null)
            statusIndicator.color = completeColor;

        if (completionText != null)
            completionText.gameObject.SetActive(true);

        isCalibrating = false;
        isCalibrated = true;

        Debug.Log("Calibration Complete!");
    }

    private IEnumerator PerformCalibration()
    {
        // If VRIK calibration is assigned, call its calibrate method
        if (vrikCalibration != null)
        {
            // Try to call Calibrate method if it exists
            var calibrateMethod = vrikCalibration.GetType().GetMethod("Calibrate");
            if (calibrateMethod != null)
            {
                calibrateMethod.Invoke(vrikCalibration, null);
            }
        }

        // Wait for calibration duration
        yield return new WaitForSeconds(calibrationDuration);
    }

    public void ResetCalibration()
    {
        isCalibrated = false;
        isCalibrating = false;

        if (titleText != null)
            titleText.text = "Ready to Calibrate";

        if (statusIndicator != null)
            statusIndicator.color = readyColor;

        if (completionText != null)
            completionText.gameObject.SetActive(false);

        if (calibrateButton != null)
            calibrateButton.interactable = true;
    }
}
