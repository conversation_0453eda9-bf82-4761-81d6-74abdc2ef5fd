using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Events;
using TMPro;
using System.Collections;

public class CalibrationManager : MonoBehaviour
{
    [<PERSON><PERSON>("UI Elements")]
    [Tooltip("The main title text - 'Ready to Calibrate'")]
    public TextMeshProUGUI titleText;

    [Tooltip("The calibrate button")]
    public Button calibrateButton;

    [<PERSON>lt<PERSON>("The status indicator circle")]
    public Image statusIndicator;

    [<PERSON>lt<PERSON>("The completion message text - 'Calibration Complete!'")]
    public TextMeshProUGUI completionText;

    [Header("Color Settings")]
    [Tooltip("Color for the status indicator when ready (gray)")]
    public Color readyColor = new Color(0.5f, 0.5f, 0.5f, 1f); // Gray

    [Tooltip("Color for the status indicator when calibrating (yellow)")]
    public Color calibratingColor = new Color(1f, 1f, 0f, 1f); // Yellow

    [Tooltip("Color for the status indicator when complete (green)")]
    public Color completeColor = new Color(0f, 1f, 0f, 1f); // Green

    [Tooltip("Color for completion text (green)")]
    public Color completionTextColor = new Color(0f, 1f, 0f, 1f); // Green

    [Header("Calibration Settings")]
    [Tooltip("Duration of calibration process in seconds")]
    public float calibrationDuration = 3f;

    [Tooltip("Reference to VRIK calibration component (optional)")]
    public MonoBehaviour vrikCalibration;

    [Tooltip("Reference to MetaAutoHandReadyNotifier to check AutoHand status")]
    public MetaAutoHandReadyNotifier autoHandNotifier;

    [Header("Calibration Events")]
    [Tooltip("Event called when calibration starts - connect calibration methods here")]
    public UnityEvent OnCalibrationStart;

    [Tooltip("Event called when calibration completes")]
    public UnityEvent OnCalibrationComplete;

    [Header("Status Colors")]
    [Tooltip("Color when waiting for AutoHand (red)")]
    public Color waitingAutoHandColor = Color.red;

    // Private variables
    private bool isCalibrating = false;
    private bool isCalibrated = false;
    private bool autoHandReady = false;

    void Start()
    {
        InitializeUI();

        // Subscribe to AutoHand ready event if notifier is assigned
        if (autoHandNotifier != null)
        {
            autoHandNotifier.OnAutoHandReady.AddListener(OnAutoHandReady);
        }
    }

    void InitializeUI()
    {
        // Check AutoHand status and set initial UI state
        bool autoHandSystemReady = CheckAutoHandStatus();

        if (autoHandSystemReady)
        {
            if (titleText != null)
                titleText.text = "Ready to Calibrate";

            if (statusIndicator != null)
                statusIndicator.color = readyColor;
        }
        else
        {
            if (titleText != null)
                titleText.text = "Waiting for AutoHand...";

            if (statusIndicator != null)
                statusIndicator.color = waitingAutoHandColor;
        }

        if (completionText != null)
        {
            completionText.text = "Calibration Complete!";
            completionText.color = completionTextColor;
            completionText.gameObject.SetActive(false);
        }

        // Setup button
        if (calibrateButton != null)
        {
            calibrateButton.onClick.AddListener(StartCalibration);
            calibrateButton.interactable = autoHandSystemReady;

            // Set button text if it has a TextMeshProUGUI component
            TextMeshProUGUI buttonText = calibrateButton.GetComponentInChildren<TextMeshProUGUI>();
            if (buttonText != null)
                buttonText.text = "Calibrate";
        }
    }

    bool CheckAutoHandStatus()
    {
        if (autoHandNotifier != null)
        {
            // Check if the notifier component is still enabled (it disables itself when ready)
            return !autoHandNotifier.enabled;
        }

        // If no notifier assigned, assume AutoHand is ready
        return true;
    }

    void OnAutoHandReady()
    {
        autoHandReady = true;

        if (titleText != null)
            titleText.text = "Ready to Calibrate";

        if (statusIndicator != null)
            statusIndicator.color = readyColor;

        if (calibrateButton != null)
            calibrateButton.interactable = true;
    }

    public void StartCalibration()
    {
        if (isCalibrating || isCalibrated)
            return;

        // Check if AutoHand is ready before starting calibration
        if (!CheckAutoHandStatus())
        {
            Debug.LogWarning("Cannot start calibration: AutoHand is not ready yet!");
            return;
        }

        StartCoroutine(CalibrationProcess());
    }

    private IEnumerator CalibrationProcess()
    {
        isCalibrating = true;

        // Update UI for calibrating state
        if (titleText != null)
            titleText.text = "Calibrating...";

        if (statusIndicator != null)
            statusIndicator.color = calibratingColor;

        if (calibrateButton != null)
            calibrateButton.interactable = false;

        // Invoke calibration start event
        OnCalibrationStart?.Invoke();

        // Perform actual calibration
        yield return StartCoroutine(PerformCalibration());

        // Update UI for completed state
        if (titleText != null)
            titleText.text = "Calibration Complete";

        if (statusIndicator != null)
            statusIndicator.color = completeColor;

        if (completionText != null)
            completionText.gameObject.SetActive(true);

        isCalibrating = false;
        isCalibrated = true;

        // Invoke calibration complete event
        OnCalibrationComplete?.Invoke();

        Debug.Log("Calibration Complete!");
    }

    private IEnumerator PerformCalibration()
    {
        // If VRIK calibration is assigned, call its calibrate method
        if (vrikCalibration != null)
        {
            // Try to call Calibrate method if it exists
            var calibrateMethod = vrikCalibration.GetType().GetMethod("Calibrate");
            if (calibrateMethod != null)
            {
                calibrateMethod.Invoke(vrikCalibration, null);
            }
        }

        // Wait for calibration duration
        yield return new WaitForSeconds(calibrationDuration);
    }

    public void ResetCalibration()
    {
        isCalibrated = false;
        isCalibrating = false;

        // Check AutoHand status before resetting UI
        bool autoHandSystemReady = CheckAutoHandStatus();

        if (titleText != null)
            titleText.text = autoHandSystemReady ? "Ready to Calibrate" : "Waiting for AutoHand...";

        if (statusIndicator != null)
            statusIndicator.color = autoHandSystemReady ? readyColor : waitingAutoHandColor;

        if (completionText != null)
            completionText.gameObject.SetActive(false);

        if (calibrateButton != null)
            calibrateButton.interactable = autoHandSystemReady;
    }
}
