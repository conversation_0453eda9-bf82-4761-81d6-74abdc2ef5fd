﻿<Project>
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp\obj\$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <UseCommonOutputDirectory>true</UseCommonOutputDirectory>
    <OutputPath>Temp\bin\Debug\</OutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <LangVersion>9.0</LangVersion>
    <RootNamespace></RootNamespace>
    <OutputType>Library</OutputType>
    <AssemblyName>Unity.VisualScripting.Core</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup>
    <NoWarn>0169;USG0001</NoWarn>
    <DefineConstants>UNITY_6000_1_12;UNITY_6000_1;UNITY_6000;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;UNITY_2023_1_OR_NEWER;UNITY_2023_2_OR_NEWER;UNITY_2023_3_OR_NEWER;UNITY_6000_0_OR_NEWER;UNITY_6000_1_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_VIRTUALTEXTURING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_MARSHALLING_TESTS;ENABLE_VIDEO;ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;TEXTCORE_1_0_OR_NEWER;EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED;PLATFORM_STANDALONE_WIN;PLATFORM_STANDALONE;UNITY_STANDALONE_WIN;UNITY_STANDALONE;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_NVIDIA;ENABLE_AMD;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_OUT_OF_PROCESS_CRASH_HANDLER;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;GFXDEVICE_WAITFOREVENT_MESSAGEPUMP;PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER;PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION;PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS;ENABLE_MONO;NET_STANDARD_2_0;NET_STANDARD;NET_STANDARD_2_1;NETSTANDARD;NETSTANDARD2_1;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER;USE_INPUT_SYSTEM_POSE_CONTROL;USE_STICK_CONTROL_THUMBSTICKS;ISDK_OPENXR_HAND;PACKAGE_INPUT_SYSTEM_EXISTS;PACKAGE_INPUT_SYSTEM_1_2_0_OR_NEWER_EXISTS;MODULE_ANIMATION_EXISTS;MODULE_PHYSICS_EXISTS;MODULE_PHYSICS_2D_EXISTS;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.23</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Game:1</UnityProjectType>
    <UnityBuildTarget>StandaloneWindows64:19</UnityBuildTarget>
    <UnityVersion>6000.1.12f1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="C:\Users\<USER>\.vscode\extensions\visualstudiotoolsforunity.vstuc-1.1.2\Analyzers\Microsoft.Unity.Analyzers.dll" />
    <Analyzer Include="C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.SourceGenerators.dll" />
    <Analyzer Include="C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.Properties.SourceGenerator.dll" />
    <Analyzer Include="C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.UIToolkit.SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\EditorBinding\TypeIconPriorityAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\InstanceActionInvoker_2.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\UIInterfaces\UnityOnEndDragMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Converters\fsEnumConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\LogicalNegationHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Cloning\Cloning.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Collections\AotList.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Converters\fsReflectedConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Serialization\SerializationVersionAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\UIInterfaces\UnityOnPointerClickMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Utilities\ReferenceCollector.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Variables\Variables.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Internal\fsPortableReflection.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\UIInterfaces\UnityOnSelectMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\StaticActionInvoker_1.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MonoBehaviour\UnityOnCollisionExitMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Converters\Unity\Rect_DirectConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\StaticActionInvoker_3.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\OperatorException.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\MemberUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Utilities\IAnalyticsIdentifiable.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Ensure\Extensions\XComparable.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\AnimatorMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Utilities\IInitializable.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\RenamedAssemblyAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\UIInterfaces\UnityOnBeginDragMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Collections\IMergedCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\IGraphItem.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MonoBehaviour\UnityOnMouseDownMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\GlobalMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\DecrementHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\ReflectionInvoker.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Collections\NonNullableList.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Converters\Unity\GUIStyleState_DirectConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Collections\INotifyCollectionChanged.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\IGraphNest.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Serialization\ISerializationDependency.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\fsPropertyAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\InstanceInvokerBase.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\GraphInstances.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Profiling\ProfilingScope.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Unity\RequiresUnityAPIAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\EditorBinding\Inspector\InspectorActionDirectionAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\fsData.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Collections\INotifiedCollectionItem.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Machines\Machine.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\OperatorUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\SerializedProperties\SerializedPropertyProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Converters\fsArrayConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\GraphSource.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\PlusHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Collections\MergedCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\StaticFunctionInvoker_5.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Connections\ConnectionCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\IGraphElementCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Utilities\HashUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Utilities\EnumUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Serialization\Converters\UnityObjectConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Variables\SavedVariables.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\TypesMatching.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Ensure\EnsureThat.NullableValueTypes.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MonoBehaviour\UnityOnCollisionEnterMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Collections\VariantList.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Namespace.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Serialization\SerializationData.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\fsObjectAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Exceptions\UnexpectedEnumValueException.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Serialization\Converters\LooseAssemblyNameConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\GraphDebugData.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\IAttributeProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\BinaryOperator.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Cloning\Cloners\FakeSerializationCloner.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Events\EventBus.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\StaticFunctionInvokerBase.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\StaticFunctionInvoker_4.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\UI\UnityOnInputFieldValueChangedMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\UIInterfaces\UnityOnScrollMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\InstanceFunctionInvoker_2.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Profiling\ProfiledSegment.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Utilities\LinqUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MonoBehaviour\UnityOnJointBreakMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Reflection\fsTypeCache.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\fsBaseConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\fsJsonPrinter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Converters\fsGuidConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\MergedGraphElementCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Collections\NonNullableCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Unity\LudiqScriptableObject.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\LessThanHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\GraphsExceptionUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\InstanceActionInvoker_5.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\StaticActionInvoker_4.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Ensure\EnsureThat.ValueTypes.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Variables\VariableDeclarationCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\IGraphElementWithData.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Unity\Singleton.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Converters\fsDictionaryConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\AdditionHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\GraphReference.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\GraphElement.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\fsAotCompilationManager.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Connections\IConnectionCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\EditorBinding\ExpectedTypeAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\UIInterfaces\UnityOnDropMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\GraphData.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Reflection\fsReflectionUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Events\EventHook.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\AndHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Variables\ApplicationVariables.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Ensure\EnsureThat.Types.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Cloning\Cloners\FieldsCloner.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Events\FrameDelayedCallback.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\EditorBinding\IInspectableAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\UI\UnityOnInputFieldEndEditMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MonoBehaviour\UnityOnTransformChildrenChangedMListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Collections\NoAllocEnumerator.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Unity\IGizmoDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\SerializedProperties\ISerializedPropertyProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\RenamedFromAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\GreaterThanOrEqualHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\EditorBinding\Inspector\InspectorAdaptiveWidthAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\UnityMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Platforms\PlatformUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Converters\fsWeakReferenceConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\IGraphElementDebugData.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Reflection\fsMetaType.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\EditorBinding\IncludeInSettingsAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\IGraphElementWithDebugData.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MonoBehaviour\UnityOnBecameInvisibleMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\InstanceFunctionInvoker_0.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\RightShiftHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Cloning\Cloners\ReflectedCloner.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Cloning\Cloners\AnimationCurveCloner.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\IGraphData.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Platforms\IAotStubbable.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Serialization\Converters\NamespaceConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Profiling\ProfilingUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\RuntimeCodebase.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Converters\Unity\GUIStyle_DirectConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MonoBehaviour\UnityOnMouseUpMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Ensure\EnsureThat.Reflection.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Unity\MacroScriptableObject.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\LessThanOrEqualHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MonoBehaviour\UnityOnCollisionStayMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Cloning\Cloners\ArrayCloner.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\MultiplicationHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\ExclusiveOrHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Input\MouseButton.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\Func_5.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\EditorBinding\Typeset.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Internal\fsVersionedType.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Macros\IMacro.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MonoBehaviour\UnityOnTriggerStay2DMListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Events\IGraphEventHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Converters\fsPrimitiveConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MonoBehaviour\UnityOnCollisionEnter2DMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\ReflectionPropertyAccessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Utilities\RuntimeVSUsageUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Converters\Unity\RectOffset_DirectConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\IGraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\OrHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\EditorBinding\NullMeansSelfAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Converters\Unity\InputAction_DirectConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Connections\IConnection.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\EqualityHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\Func_6.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Internal\fsCyclicReferenceManager.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Ensure\Extensions\XString.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Pooling\ArrayPool.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Cloning\Cloners\GradientCloner.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\UI\UnityOnButtonClickMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Cloning\Cloners\DictionaryCloner.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Converters\fsTypeConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\IGraphEventListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\InstanceFunctionInvoker_4.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\LooseAssemblyName.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Utilities\IIdentifiable.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\EditorBinding\Inspector\InspectorWideAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MonoBehaviour\UnityOnControllerColliderHitMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\UnaryOperator.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MonoBehaviour\UnityOnTriggerEnterMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Serialization\Converters\Ray2DConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MonoBehaviour\UnityOnMouseUpAsButtonMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Internal\fsVersionManager.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Collections\MergedKeyedCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Variables\VariableDeclaration.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\EditorBinding\TypeSetAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\EditorBinding\PredictableAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Unity\UnityObjectOwnershipUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\GraphPointerException.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Connections\GraphConnectionCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\EditorBinding\Inspector\InspectorExpandTooltipAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\UnaryOperatorHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Pooling\GenericPool.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Converters\Unity\Gradient_DirectConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Utilities\Recursion.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\MemberInfoComparer.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\TypeName.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\fsResult.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\fsObjectProcessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Variables\IGraphDataWithVariables.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Converters\Unity\Keyframe_DirectConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\UIInterfaces\UnityOnDeselectMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Serialization\ISerializationDepender.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\fsJsonParser.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Utilities\CoroutineRunner.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\GraphPointer.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Variables\VariableDeclarationsCloner.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\ConversionUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\AssemblyQualifiedNameParser\ParsedAssemblyQualifiedName.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\IOptimizedInvoker.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\UIInterfaces\UnityOnMoveMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Machines\IMachine.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Internal\fsTypeExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\GreaterThanHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\EditorBinding\TypeIconAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\IPrewarmable.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\BinaryOperatorHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\InstancePropertyAccessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Converters\fsKeyValuePairConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\GenericClosingException.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Ensure\EnsureThat.Booleans.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Ensure\EnsureThat.Guids.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Variables\VariablesSaver.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Exceptions\DebugUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\UIInterfaces\UnityOnCancelMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\DivisionHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Pooling\IPoolable.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\EditorBinding\InspectableAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MonoBehaviour\UnityOnMouseOverMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Utilities\Empty.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\EditorBinding\InspectableIfAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\InequalityHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Ensure\EnsureThat.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\InstanceActionInvoker_1.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\InstanceFunctionInvokerBase.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\TypeNameDetail.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Platforms\AotIncompatibleAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\InvalidOperatorException.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\GraphStack.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Collections\AotDictionary.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Connections\ConnectionCollectionBase.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\IGraphRoot.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Decorators\IDecoratorAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Exceptions\InvalidConversionException.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Ensure\EnsureThat.Collections.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\IGraphEventListenerData.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Converters\Unity\UnityEvent_Converter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Converters\fsIEnumerableConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\InvokerBase.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Collections\VariantKeyedCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Events\EventHooks.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\TypeFilter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\Action_5.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Collections\NonNullableDictionary.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Variables\VariablesAsset.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Groups\GraphGroup.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Converters\fsNullableConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\InstanceFunctionInvoker_1.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\UIInterfaces\UnityOnPointerExitMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\GraphNest.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MonoBehaviour\UnityOnBecameVisibleMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\StaticActionInvoker_5.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\UI\UnityOnScrollRectValueChangedMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Collections\WatchedList.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\IGraphElement.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Collections\GuidCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Attributes\VisualScriptingHelpURLAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Decorators\ValueAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MonoBehaviour\UnityOnTriggerExitMListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\AttributeUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Variables\ObjectVariables.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\TypeUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\InstanceActionInvoker_3.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\StaticFunctionInvoker_1.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\OperatorHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\Graph.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\StaticInvokerBase.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\StaticFieldAccessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\StaticFunctionInvoker_3.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MonoBehaviour\UnityOnTransformParentChangedMListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MonoBehaviour\UnityOnJointBreak2DMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Converters\fsDateConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Properties\AssemblyInfo.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Serialization\SerializeAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\LeftShiftHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\EditorBinding\Inspector\InspectorLabelAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\UI\UnityOnDropdownValueChangedMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\Action_6.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\fsMemberSerialization.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Collections\MergedList.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Converters\Unity\Bounds_DirectConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\NumericNegationHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Variables\VariableDeclarations.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Pooling\DictionaryPool.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\InstanceFunctionInvoker_3.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Converters\Unity\LayerMask_DirectConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Converters\fsForwardConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MonoBehaviour\UnityOnMouseEnterMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Ensure\EnsureThat.Objects.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\fsConfig.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Utilities\IGettable.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Unity\SceneSingleton.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\UIInterfaces\UnityOnDragMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Collections\IKeyedCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Cloning\ISpecifiesCloner.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Serialization\DictionaryAsset.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\EditorBinding\Inspector\InspectorTextAreaAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Macros\Macro.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MonoBehaviour\UnityOnParticleCollisionMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Serialization\SerializationOperation.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\EditorBinding\EditorTimeBinding.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MonoBehaviour\UnityOnTriggerEnter2DMListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Cloning\Cloners\ListCloner.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\EditorBinding\WarnBeforeRemovingAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Events\EventHookComparer.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\ReflectionFieldAccessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\StaticActionInvoker_2.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\UI\UnityOnToggleValueChangedMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\fsContext.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\IGraphParentElement.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MonoBehaviour\UnityOnCollisionExit2DMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Utilities\OverrideStack.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\IGraphDebugData.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\fsIgnoreAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\fsConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MonoBehaviour\UnityOnTriggerStayMListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\IncrementHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Utilities\ReferenceEqualityComparer.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\StaticPropertyAccessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\StaticFunctionInvoker_0.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\StickyNote\StickyNote.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Pooling\ListPool.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Ensure\EnsureThat.Comparables.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\EditorBinding\Inspector\InspectorToggleLeftAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Cloning\Cloners\EnumerableCloner.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Input\PressState.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Collections\ISet.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MonoBehaviour\UnityOnTriggerExit2DMListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\EditorBinding\DisableAnnotationAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MonoBehaviour\UnityOnCollisionStay2DMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Internal\fsOption.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Pooling\HashSetPool.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Variables\VariableKindAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\RenamedNamespaceAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\IGraphElementData.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\UI\UnityOnSliderValueChangedMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Ensure\Ensure.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MonoBehaviour\UnityOnMouseExitMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Profiling\ProfiledSegmentCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\InstanceActionInvoker_4.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Utilities\XColor.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Utilities\ExceptionUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Member.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Collections\IProxyableNotifyCollectionChanged.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Collections\DebugDictionary.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Events\IEventGraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\StaticActionInvoker_0.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\fsDirectConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\UIInterfaces\UnityOnSubmitMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Events\IEventMachine.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Unity\IUnityObjectOwnable.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\InstanceActionInvokerBase.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Collections\FlexibleDictionary.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Serialization\DoNotSerializeAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Cloning\ICloner.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Utilities\StringUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Variables\InspectorVariableNameAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\SerializedProperties\SerializedPropertyProviderAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\MonoBehaviour\UnityOnMouseDragMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Utilities\UnityObjectUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\EditorBinding\Inspector\InspectorDelayedAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\fsConverterRegistrar.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Unity\SingletonAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\AmbiguousOperatorException.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Connections\InvalidConnectionException.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Events\EmptyEventArgs.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Variables\SceneVariables.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Serialization\Serialization.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\InstanceFunctionInvoker_5.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Serialization\SerializableType.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Variables\VariableKind.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Ensure\ExceptionMessages.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\UIInterfaces\UnityOnPointerUpMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\OptimizedReflection.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Converters\Unity\AnimationCurve_DirectConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\ActionDirection.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\MemberFilter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Unity\ISingleton.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\IGraphParent.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\StaticFunctionInvoker_2.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Pooling\ManualPool.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\IGraphNesterElement.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Unity\LudiqBehaviour.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Serialization\Converters\RayConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\UIInterfaces\UnityOnPointerDownMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\UI\UnityOnScrollbarValueChangedMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\InstanceActionInvoker_0.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Unity\UnityThread.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Cloning\CloningContext.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Utilities\ComponentHolderProtocol.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\IGraphNester.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\EditorBinding\AllowsNullAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Listeners\UIInterfaces\UnityOnPointerEnterMessageListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\InstanceFieldAccessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\StaticActionInvokerBase.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\fsISerializationCallbacks.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\fsSerializer.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Variables\IGraphWithVariables.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Collections\VariantCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\EditorBinding\Inspector\InspectorRangeAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\EditorBinding\EditorBindingUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\EditorBinding\WarnBeforeEditingAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\ModuloHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Utilities\CSharpNameUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Events\EventMachine.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Graphs\GraphElementCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\TypeQualifier.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\Reflection\fsMetaProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Exceptions\InvalidImplementationException.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Optimization\IOptimizedAccessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Reflection\Operators\SubtractionHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Serialization\SerializeAsAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\EditorBinding\Inspector\InspectViaImplementationsAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Dependencies\FullSerializer\fsExceptions.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Ensure\EnsureThat.Strings.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Collections\NonNullableHashSet.cs" />
    <Compile Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Cloning\Cloner.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Core\Unity.VisualScripting.Core.asmdef" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.ContentLoadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GraphicsStateCollectionSerializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HierarchyCoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.HierarchyCoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputForUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputForUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MarshallingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.MarshallingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MultiplayerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ShaderVariantAnalyticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.ShaderVariantAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.VirtualTexturingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AccessibilityModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AdaptivePerformanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.AdaptivePerformanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.BuildProfileModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.BuildProfileModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreBusinessMetricsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreBusinessMetricsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.EditorToolbarModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EmbreeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.EmbreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphicsStateCollectionSerializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridAndSnapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridAndSnapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.MultiplayerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Physics2DModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.PresetsUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PropertiesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SafeModeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.SafeModeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.ShaderFoundryModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.ShaderFoundryModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SketchUpModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.SketchUpModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteMaskModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteShapeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SubstanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TerrainModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextRenderingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TilemapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TreeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.TreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIAutomationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIAutomationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UmbraModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VFXModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VideoModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.XRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEngine\UnityEditor.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Graphs">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\Managed\UnityEditor.Graphs.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.WebGL.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\WebGLSupport\UnityEditor.WebGL.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.OSXStandalone.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\MacStandaloneSupport\UnityEditor.OSXStandalone.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VisionOS.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\VisionOSPlayer\UnityEditor.VisionOS.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.WindowsStandalone.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\WindowsStandaloneSupport\UnityEditor.WindowsStandalone.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UWP.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\MetroSupport\UnityEditor.UWP.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Android.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\AndroidPlayer\UnityEditor.Android.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AppleTV.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\AppleTVSupport\UnityEditor.AppleTV.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.LinuxStandalone.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\LinuxStandaloneSupport\UnityEditor.LinuxStandalone.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.YamlDotNet">
      <HintPath>Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>Library\PackageCache\com.unity.collections@56bff8827a7e\Unity.Collections.LowLevel.ILSupport\Unity.Collections.LowLevel.ILSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>Library\PackageCache\com.unity.ext.nunit@031a54704bff\net40\unity-custom\nunit.framework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="unityplastic">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\unityplastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Antlr3.Runtime">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Unity.Plastic.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Newtonsoft.Json">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Unity.Plastic.Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="log4netPlastic">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\log4netPlastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.IonicZip">
      <HintPath>Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Antlr3.Runtime">
      <HintPath>Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Runtime\VisualScripting.Flow\Dependencies\NCalc\Unity.VisualScripting.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>Library\PackageCache\com.unity.nuget.newtonsoft-json@74deb55db2a0\Runtime\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.TextureAssets">
      <HintPath>Library\PackageCache\com.unity.visualscripting@6279e2b7c485\Editor\VisualScripting.Core\EditorAssetResources\Unity.VisualScripting.TextureAssets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>Library\PackageCache\com.unity.nuget.mono-cecil@d6f9955a5d5f\Mono.Cecil.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DemiEditor">
      <HintPath>Assets\Plugins\Demigiant\DemiLib\Core\Editor\DemiEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>Assets\Plugins\Demigiant\DOTween\DOTween.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenUpgradeManager">
      <HintPath>Assets\Plugins\Demigiant\DOTween\Editor\DOTweenUpgradeManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenPro">
      <HintPath>Assets\Plugins\Demigiant\DOTweenPro\DOTweenPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DemiLib">
      <HintPath>Assets\Plugins\Demigiant\DemiLib\Core\DemiLib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenProEditor">
      <HintPath>Assets\Plugins\Demigiant\DOTweenPro\Editor\DOTweenProEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenEditor">
      <HintPath>Assets\Plugins\Demigiant\DOTween\Editor\DOTweenEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\AppleTVSupport\UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Apple.Extensions.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\AppleTVSupport\UnityEditor.Apple.Extensions.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Apple.Extensions.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.Apple.Extensions.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Types">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Types.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Gradle">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Gradle.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\VisionOSPlayer\UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Apple.Extensions.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\VisionOSPlayer\UnityEditor.Apple.Extensions.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\PlaybackEngines\MacStandaloneSupport\UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\ref\2.1.0\netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Buffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tracing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.DispatchProxy.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\Extensions\2.0.0\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Web">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ServiceModel.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Web">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Windows">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Windows.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Serialization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.12f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Unity.InputSystem.csproj" />
    <ProjectReference Include="UnityEditor.UI.csproj" />
    <ProjectReference Include="UnityEngine.UI.csproj" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>
